#!/usr/bin/env python3
"""
Verify SystemVerilog logic against Python disassembler for all bit widths
"""

from tile_disassembler import TileDisassembler

def test_all_bitwidths():
    disasm = TileDisassembler()
    
    # Test cases for all bit widths
    test_cases = [
        # 32-bit instructions
        (0x407b, 32, "32-bit CSR instruction"),
        
        # 64-bit instructions  
        (0x8000007b0000607b, 64, "64-bit standard memory load"),
        (0x8003907b8200647b, 64, "64-bit block memory load"),
        
        # 96-bit instructions
        (0xf260707b8000007b0000697b, 96, "96-bit indexed memory load"),
        
        # 128-bit instructions
        (0xf260707bf260707b8000047b3800617b, 128, "128-bit tile copy"),
    ]
    
    print("Python Disassembler Verification")
    print("=" * 60)
    
    for instruction, bit_width, description in test_cases:
        print(f"\n{description}:")
        print(f"  Input:  0x{instruction:0{bit_width//4}x} ({bit_width}-bit)")
        
        # Get Python result
        result = disasm.disassemble_instruction(instruction, bit_width)
        print(f"  Python: {result}")
        
        # Analyze instruction structure
        print(f"  Analysis:")
        
        # Split into 32-bit words
        num_words = bit_width // 32
        words = []
        for i in range(num_words):
            word = (instruction >> (i * 32)) & 0xFFFFFFFF
            words.append(word)
            print(f"    Word {i}: 0x{word:08x}")
        
        # Analyze first word for length detection
        word1 = words[0]
        ace_op = word1 & 0x7F
        tuop = (word1 >> 12) & 0x7
        lsuop = (word1 >> 10) & 0x3
        memuop = (word1 >> 25) & 0x3F
        
        print(f"    First word analysis:")
        print(f"      ACE_OP: 0x{ace_op:02x}")
        print(f"      tuop: {tuop}")
        print(f"      lsuop: {lsuop}")
        print(f"      memuop: {memuop}")
        
        # Predict SystemVerilog behavior
        predicted_length = predict_sv_length(word1)
        print(f"    Predicted SV length: {predicted_length}")
        
        # Check if prediction matches actual bit width
        expected_sv_length = {32: "INSTR_32BIT", 64: "INSTR_64BIT", 
                             96: "INSTR_96BIT", 128: "INSTR_128BIT"}[bit_width]
        
        if predicted_length == expected_sv_length:
            print(f"    Length prediction: CORRECT ✓")
        else:
            print(f"    Length prediction: INCORRECT ✗ (expected {expected_sv_length})")

def predict_sv_length(first_word):
    """Predict SystemVerilog get_instruction_length behavior"""
    ace_op = first_word & 0x7F
    
    if ace_op != 0x7b:
        return "INSTR_32BIT"
    
    tuop = (first_word >> 12) & 0x7
    
    if tuop == 0:  # tuop_000
        memuop = (first_word >> 25) & 0x3F
        lsuop = (first_word >> 10) & 0x3
        
        if lsuop == 0:
            return "INSTR_64BIT"  # Linear/stride
        elif lsuop == 1:
            return "INSTR_64BIT"  # Other 64-bit
        elif lsuop == 2:
            return "INSTR_96BIT"  # Index (3-lane)
        elif lsuop == 3:
            if memuop == 0b011100:  # tacp operations
                return "INSTR_128BIT"
            else:
                return "INSTR_64BIT"
    
    elif tuop == 1:  # tuop_001
        return "INSTR_64BIT"
    elif tuop == 2:  # tuop_010
        return "INSTR_64BIT"
    elif tuop == 3:  # tuop_011
        return "INSTR_64BIT"
    elif tuop == 4:  # tuop_100
        return "INSTR_32BIT"
    elif tuop == 5:  # tuop_101
        return "INSTR_32BIT"
    elif tuop == 6:  # tuop_110
        lsuop = (first_word >> 10) & 0x3
        memuop = (first_word >> 25) & 0x3F

        if memuop == 1:  # 000001
            return "INSTR_64BIT"  # Block memory (dual-lane)
        elif memuop == 0:  # 000000
            if lsuop == 0:
                return "INSTR_64BIT"  # Linear operations (dual-lane)
            elif lsuop == 1:
                return "INSTR_64BIT"  # Stride operations (dual-lane)
            elif lsuop == 2:
                return "INSTR_96BIT"  # Index operations (3-lane)
            elif lsuop == 3:
                return "INSTR_64BIT"  # Other 64-bit operations
        elif memuop == 0b011100:  # 011100
            return "INSTR_128BIT"  # Tile copy (4-lane)
        else:
            return "INSTR_32BIT"  # ACE operations
    elif tuop == 7:  # tuop_111
        return "INSTR_32BIT"
    
    return "INSTR_32BIT"

def analyze_specific_cases():
    """Analyze specific problematic cases"""
    print("\n" + "=" * 60)
    print("Specific Case Analysis")
    print("=" * 60)
    
    # Cases that might be problematic
    cases = [
        (0x0000697b, "96-bit first word"),
        (0x3800617b, "128-bit first word"),
        (0x8200647b, "64-bit block memory first word"),
    ]
    
    for word, description in cases:
        print(f"\n{description}: 0x{word:08x}")
        
        # Analyze fields
        ace_op = word & 0x7F
        tuop = (word >> 12) & 0x7
        lsuop = (word >> 10) & 0x3
        memuop = (word >> 25) & 0x3F
        
        print(f"  ACE_OP: 0x{ace_op:02x} ({'tile' if ace_op == 0x7b else 'non-tile'})")
        print(f"  tuop: {tuop}")
        print(f"  lsuop: {lsuop}")
        print(f"  memuop: {memuop} (0b{memuop:06b})")
        
        predicted = predict_sv_length(word)
        print(f"  Predicted length: {predicted}")

if __name__ == "__main__":
    test_all_bitwidths()
    analyze_specific_cases()
