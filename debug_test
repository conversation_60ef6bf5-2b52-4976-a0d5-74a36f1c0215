#! /usr/bin/vvp
:ivl_version "11.0 (stable)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/system.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_sys.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_textio.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2005_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/va_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2009.vpi";
S_0x5613d4e62190 .scope package, "$unit" "$unit" 2 1;
 .timescale 0 0;
S_0x5613d4e62320 .scope module, "debug_test" "debug_test" 3 3;
 .timescale 0 0;
S_0x5613d4e905a0 .scope begin, "$unm_blk_1" "$unm_blk_1" 3 5, 3 5 0, S_0x5613d4e62320;
 .timescale 0 0;
v0x5613d4e63750_0 .var "ace_op", 6 0;
v0x5613d4e63bb0_0 .var "ctrluop", 2 0;
v0x5613d4e63fa0_0 .var "isMem", 0 0;
v0x5613d4eb7e10_0 .var "test_instruction", 31 0;
v0x5613d4eb7ef0_0 .var "tuop", 2 0;
v0x5613d4eb8020_0 .var "waitop", 2 0;
    .scope S_0x5613d4e62320;
T_0 ;
    %fork t_1, S_0x5613d4e905a0;
    %jmp t_0;
    .scope S_0x5613d4e905a0;
t_1 ;
    %vpi_call/w 3 13 "$display", "=== Debug twait test ===" {0 0 0};
    %pushi/vec4 1887457403, 0, 32;
    %store/vec4 v0x5613d4eb7e10_0, 0, 32;
    %vpi_call/w 3 17 "$display", "Testing instruction: 0x%08x", v0x5613d4eb7e10_0 {0 0 0};
    %load/vec4 v0x5613d4eb7e10_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0x5613d4e63750_0, 0, 7;
    %load/vec4 v0x5613d4eb7e10_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0x5613d4eb7ef0_0, 0, 3;
    %load/vec4 v0x5613d4eb7e10_0;
    %parti/s 3, 23, 6;
    %store/vec4 v0x5613d4e63bb0_0, 0, 3;
    %load/vec4 v0x5613d4eb7e10_0;
    %parti/s 3, 28, 6;
    %store/vec4 v0x5613d4eb8020_0, 0, 3;
    %load/vec4 v0x5613d4eb7e10_0;
    %parti/s 1, 26, 6;
    %store/vec4 v0x5613d4e63fa0_0, 0, 1;
    %vpi_call/w 3 26 "$display", "ace_op: %b (%d)", v0x5613d4e63750_0, v0x5613d4e63750_0 {0 0 0};
    %vpi_call/w 3 27 "$display", "tuop: %b (%d)", v0x5613d4eb7ef0_0, v0x5613d4eb7ef0_0 {0 0 0};
    %vpi_call/w 3 28 "$display", "ctrluop: %b (%d)", v0x5613d4e63bb0_0, v0x5613d4e63bb0_0 {0 0 0};
    %vpi_call/w 3 29 "$display", "waitop: %b (%d)", v0x5613d4eb8020_0, v0x5613d4eb8020_0 {0 0 0};
    %vpi_call/w 3 30 "$display", "isMem: %b (%d)", v0x5613d4e63fa0_0, v0x5613d4e63fa0_0 {0 0 0};
    %load/vec4 v0x5613d4e63750_0;
    %cmpi/e 123, 0, 7;
    %jmp/0xz  T_0.0, 4;
    %vpi_call/w 3 34 "$display", "\342\234\223 ACE_OP matches tile instruction" {0 0 0};
    %load/vec4 v0x5613d4eb7ef0_0;
    %cmpi/e 5, 0, 3;
    %jmp/0xz  T_0.2, 4;
    %vpi_call/w 3 37 "$display", "\342\234\223 tuop matches sync operations (101)" {0 0 0};
    %load/vec4 v0x5613d4e63bb0_0;
    %cmpi/e 1, 0, 3;
    %jmp/0xz  T_0.4, 4;
    %vpi_call/w 3 40 "$display", "\342\234\223 ctrluop matches twait operations (001)" {0 0 0};
    %load/vec4 v0x5613d4eb8020_0;
    %cmpi/e 7, 0, 3;
    %jmp/0xz  T_0.6, 4;
    %vpi_call/w 3 43 "$display", "\342\234\223 waitop matches basic twait (111)" {0 0 0};
    %vpi_call/w 3 44 "$display", "\342\234\223 This should be identified as 'twait' or 'twait.mem'" {0 0 0};
    %load/vec4 v0x5613d4e63fa0_0;
    %cmpi/e 0, 0, 1;
    %jmp/0xz  T_0.8, 4;
    %vpi_call/w 3 47 "$display", "\342\234\223 isMem=0, should be 'twait'" {0 0 0};
    %jmp T_0.9;
T_0.8 ;
    %vpi_call/w 3 49 "$display", "\342\234\223 isMem=1, should be 'twait.mem'" {0 0 0};
T_0.9 ;
    %jmp T_0.7;
T_0.6 ;
    %vpi_call/w 3 52 "$display", "\342\234\227 waitop does not match basic twait" {0 0 0};
T_0.7 ;
    %jmp T_0.5;
T_0.4 ;
    %vpi_call/w 3 55 "$display", "\342\234\227 ctrluop does not match twait operations" {0 0 0};
T_0.5 ;
    %jmp T_0.3;
T_0.2 ;
    %vpi_call/w 3 58 "$display", "\342\234\227 tuop does not match sync operations" {0 0 0};
T_0.3 ;
    %jmp T_0.1;
T_0.0 ;
    %vpi_call/w 3 61 "$display", "\342\234\227 ACE_OP does not match tile instruction" {0 0 0};
T_0.1 ;
    %vpi_call/w 3 64 "$display", "=== Debug Complete ===" {0 0 0};
    %end;
    .scope S_0x5613d4e62320;
t_0 %join;
    %end;
    .thread T_0;
# The file index is used to find the file name in the following table.
:file_names 4;
    "N/A";
    "<interactive>";
    "-";
    "debug_test.sv";
