#!/usr/bin/env python3
"""
Final test to simulate the complete SystemVerilog disassembly process
for instruction 0x8003907b8200647b
"""

def simulate_complete_disassembly():
    instruction = 0x8003907b8200647b
    
    print("=== 完整反编译过程模拟 ===")
    print(f"指令: 0x{instruction:016x}")
    print()
    
    # Step 1: 检查是否为tile指令
    word1 = instruction & 0xFFFFFFFF
    ace_op = word1 & 0x7F
    is_tile = (ace_op == 0x7b)
    
    print(f"Step 1: is_tile_instruction = {is_tile}")
    if not is_tile:
        print("不是tile指令，退出")
        return
    
    # Step 2: 确定指令长度
    tuop = (word1 >> 12) & 0x7
    memuop = (word1 >> 25) & 0x3F
    lsuop = (word1 >> 10) & 0x3
    
    print(f"Step 2: get_instruction_length")
    print(f"  tuop = {tuop}")
    print(f"  memuop = {memuop}")
    print(f"  lsuop = {lsuop}")
    
    if tuop == 6 and memuop == 1:  # tuop_110 + memuop_000001
        length = 64  # INSTR_64BIT
        print(f"  length = {length} bits")
    else:
        print(f"  未知长度组合")
        return
    
    # Step 3: 提取指令名称
    print(f"Step 3: extract_instruction_name")
    
    # 检查第二个word的tuop
    word2 = (instruction >> 32) & 0xFFFFFFFF
    second_tuop = (word2 >> 12) & 0x7
    
    print(f"  second_tuop = {second_tuop}")
    
    if tuop == 6 and length == 64:  # tuop_110, 64-bit
        if memuop == 1:  # Block memory operations
            if second_tuop == 1:  # tuop_001
                if lsuop == 1:
                    instr_name = "tld.trr.blk.mx48.share"
                elif lsuop == 2:
                    instr_name = "tld.trr.blk.mx6.share"
                elif lsuop == 0:
                    instr_name = "tld.trr.blk.share"
                else:
                    instr_name = "unknown_blk_lsuop"
            else:
                instr_name = "unknown_blk_second_tuop"
        else:
            instr_name = "unknown_memuop"
    else:
        instr_name = "unknown_pattern"
    
    print(f"  instr_name = {instr_name}")
    
    # Step 4: 格式化操作数
    print(f"Step 4: format_operands")
    
    if instr_name.startswith("tld") and "trr" in instr_name:
        # 使用修复后的字段位置
        td = (instruction >> 55) & 0xFF      # bits [62:55]
        rs1 = (instruction >> 47) & 0x1F     # bits [51:47]
        rs2 = (instruction >> 20) & 0x1F     # bits [24:20]
        
        print(f"  td = {td}")
        print(f"  rs1 = {rs1}")
        print(f"  rs2 = {rs2}")
        
        operands = f"t{td}, (x{rs1}), x{rs2}"
    else:
        operands = ""
    
    print(f"  operands = {operands}")
    
    # Step 5: 组合最终结果
    if operands:
        result = f"{instr_name} {operands}"
    else:
        result = instr_name
    
    print()
    print(f"最终结果: {result}")
    print(f"期望结果: tld.trr.blk.mx48.share t0, (x7), x0")
    
    if result == "tld.trr.blk.mx48.share t0, (x7), x0":
        print("✓ 完美匹配!")
    else:
        print("✗ 不匹配")
    
    print()
    print("=== 问题诊断 ===")
    
    # 如果你看到的是 t123, (x3)，可能的原因：
    print("如果实际输出是 't123, (x3)'，可能的原因:")
    print("1. 字段提取位置仍然错误")
    print("2. SystemVerilog中的位选择语法问题")
    print("3. 指令数据在传递过程中被修改")
    print("4. 其他代码路径被执行")
    
    # 检查错误的字段提取会产生什么结果
    print()
    print("使用旧的错误字段位置:")
    td_old = (word2 >> 23) & 0xFF  # 旧的 [32+30:32+23]
    rs1_old = (word2 >> 15) & 0x1F  # 旧的 [32+19:32+15]
    
    print(f"  td_old = {td_old}")
    print(f"  rs1_old = {rs1_old}")
    print(f"  这会产生: t{td_old}, (x{rs1_old}), x{rs2}")
    
    # 检查是否有位移错误
    print()
    print("检查可能的位移错误:")
    
    # 如果td=123，那么对应的位模式是什么？
    if td_old == 123:
        print(f"td=123 (0x{123:02x}) 对应二进制: {123:08b}")
        print("这表明可能从错误的位位置提取了数据")

if __name__ == "__main__":
    simulate_complete_disassembly()
