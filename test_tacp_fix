#! /usr/bin/vvp
:ivl_version "11.0 (stable)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/system.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_sys.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_textio.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2005_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/va_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2009.vpi";
S_0x55ac49c5fcb0 .scope package, "$unit" "$unit" 2 1;
 .timescale 0 0;
S_0x55ac49c38c40 .scope package, "tile_decoder_pkg" "tile_decoder_pkg" 3 5;
 .timescale 0 0;
P_0x55ac49c38dd0 .param/l "TILE_ACE_OP" 0 3 16, C4<1111011>;
enum0x55ac49bcbf80 .enum4 (2)
   "INSTR_32BIT" 2'b00,
   "INSTR_64BIT" 2'b01,
   "INSTR_96BIT" 2'b10,
   "INSTR_128BIT" 2'b11
 ;
S_0x55ac49c3e3a0 .scope autofunction.vec4.s135, "add_word_to_collector" "add_word_to_collector" 3 137, 3 137 0, S_0x55ac49c38c40;
 .timescale 0 0;
; Variable add_word_to_collector is vec4 return value of scope S_0x55ac49c3e3a0
v0x55ac49ca8520_0 .var "collector", 134 0;
v0x55ac49ca8600_0 .var "new_collector", 134 0;
v0x55ac49ca86c0_0 .var "word", 31 0;
TD_tile_decoder_pkg.add_word_to_collector ;
    %load/vec4 v0x55ac49ca8520_0;
    %store/vec4 v0x55ac49ca8600_0, 0, 135;
    %load/vec4 v0x55ac49ca8520_0;
    %parti/u 1, 1, 32;
    %nor/r;
    %load/vec4 v0x55ac49ca8520_0;
    %parti/u 3, 4, 32;
    %pad/u 32;
    %cmpi/u 4, 0, 32;
    %flag_get/vec4 5;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.0, 8;
    %load/vec4 v0x55ac49ca8520_0;
    %parti/u 3, 4, 32;
    %dup/vec4;
    %pushi/vec4 1, 0, 3;
    %cmp/u;
    %jmp/1 T_0.2, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 3;
    %cmp/u;
    %jmp/1 T_0.3, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 3;
    %cmp/u;
    %jmp/1 T_0.4, 6;
    %jmp T_0.5;
T_0.2 ;
    %load/vec4 v0x55ac49ca86c0_0;
    %ix/load 4, 39, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55ac49ca8600_0, 4, 32;
    %jmp T_0.5;
T_0.3 ;
    %load/vec4 v0x55ac49ca86c0_0;
    %ix/load 4, 71, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55ac49ca8600_0, 4, 32;
    %jmp T_0.5;
T_0.4 ;
    %load/vec4 v0x55ac49ca86c0_0;
    %ix/load 4, 103, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55ac49ca8600_0, 4, 32;
    %jmp T_0.5;
T_0.5 ;
    %pop/vec4 1;
    %load/vec4 v0x55ac49ca8520_0;
    %parti/u 3, 4, 32;
    %addi 1, 0, 3;
    %ix/load 4, 4, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55ac49ca8600_0, 4, 3;
    %load/vec4 v0x55ac49ca8520_0;
    %parti/u 2, 2, 32;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_0.6, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_0.7, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_0.8, 6;
    %pushi/vec4 1, 0, 1;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55ac49ca8600_0, 4, 1;
    %jmp T_0.10;
T_0.6 ;
    %pushi/vec4 2, 0, 32;
    %load/vec4 v0x55ac49ca8600_0;
    %parti/u 3, 4, 32;
    %pad/u 32;
    %cmp/u;
    %flag_get/vec4 4;
    %flag_get/vec4 5;
    %or;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55ac49ca8600_0, 4, 1;
    %jmp T_0.10;
T_0.7 ;
    %pushi/vec4 3, 0, 32;
    %load/vec4 v0x55ac49ca8600_0;
    %parti/u 3, 4, 32;
    %pad/u 32;
    %cmp/u;
    %flag_get/vec4 4;
    %flag_get/vec4 5;
    %or;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55ac49ca8600_0, 4, 1;
    %jmp T_0.10;
T_0.8 ;
    %pushi/vec4 4, 0, 32;
    %load/vec4 v0x55ac49ca8600_0;
    %parti/u 3, 4, 32;
    %pad/u 32;
    %cmp/u;
    %flag_get/vec4 4;
    %flag_get/vec4 5;
    %or;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55ac49ca8600_0, 4, 1;
    %jmp T_0.10;
T_0.10 ;
    %pop/vec4 1;
T_0.0 ;
    %load/vec4 v0x55ac49ca8600_0;
    %ret/vec4 0, 0, 135;  Assign to add_word_to_collector (store_vec4_to_lval)
    %disable S_0x55ac49c3e3a0;
    %end;
S_0x55ac49ca87a0 .scope autofunction.str, "disassemble_instruction" "disassemble_instruction" 3 445, 3 445 0, S_0x55ac49c38c40;
 .timescale 0 0;
; Variable disassemble_instruction is string return value of scope S_0x55ac49ca87a0
v0x55ac49ca8a60_0 .var/str "instr_name";
v0x55ac49ca8b20_0 .var "instruction_data", 127 0;
v0x55ac49ca8be0_0 .var "length", 1 0;
v0x55ac49ca8cc0_0 .var/str "operands";
v0x55ac49ca8dd0_0 .var/str "result";
TD_tile_decoder_pkg.disassemble_instruction ;
    %alloc S_0x55ac49ca9230;
    %load/vec4 v0x55ac49ca8b20_0;
    %load/vec4 v0x55ac49ca8be0_0;
    %store/vec4 v0x55ac49caa450_0, 0, 2;
    %store/vec4 v0x55ac49caa390_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.extract_instruction_name, S_0x55ac49ca9230;
    %free S_0x55ac49ca9230;
    %store/str v0x55ac49ca8a60_0;
    %alloc S_0x55ac49caaa80;
    %load/vec4 v0x55ac49ca8b20_0;
    %load/vec4 v0x55ac49ca8be0_0;
    %load/str v0x55ac49ca8a60_0;
    %store/str v0x55ac49cac600_0;
    %store/vec4 v0x55ac49cac780_0, 0, 2;
    %store/vec4 v0x55ac49cac6c0_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.format_operands, S_0x55ac49caaa80;
    %free S_0x55ac49caaa80;
    %store/str v0x55ac49ca8cc0_0;
    %load/str v0x55ac49ca8cc0_0;
    %pushi/str "";
    %cmp/str;
    %flag_inv 4;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_1.11, 8;
    %vpi_call/w 3 455 "$sformat", v0x55ac49ca8dd0_0, "%s %s", v0x55ac49ca8a60_0, v0x55ac49ca8cc0_0 {0 0 0};
    %jmp T_1.12;
T_1.11 ;
    %load/str v0x55ac49ca8a60_0;
    %store/str v0x55ac49ca8dd0_0;
T_1.12 ;
    %load/str v0x55ac49ca8dd0_0;
    %ret/str 0; Assign to disassemble_instruction
    %disable S_0x55ac49ca87a0;
    %end;
S_0x55ac49ca8e90 .scope autofunction.vec4.s7, "extract_ace_op" "extract_ace_op" 3 28, 3 28 0, S_0x55ac49c38c40;
 .timescale 0 0;
; Variable extract_ace_op is vec4 return value of scope S_0x55ac49ca8e90
v0x55ac49ca9150_0 .var "word", 31 0;
TD_tile_decoder_pkg.extract_ace_op ;
    %load/vec4 v0x55ac49ca9150_0;
    %parti/s 7, 0, 2;
    %ret/vec4 0, 0, 7;  Assign to extract_ace_op (store_vec4_to_lval)
    %disable S_0x55ac49ca8e90;
    %end;
S_0x55ac49ca9230 .scope autofunction.str, "extract_instruction_name" "extract_instruction_name" 3 178, 3 178 0, S_0x55ac49c38c40;
 .timescale 0 0;
v0x55ac49caa1d0_0 .var "ace_op", 6 0;
; Variable extract_instruction_name is string return value of scope S_0x55ac49ca9230
v0x55ac49caa390_0 .var "instruction_data", 127 0;
v0x55ac49caa450_0 .var "length", 1 0;
v0x55ac49caa530_0 .var "lsuop", 1 0;
v0x55ac49caa660_0 .var "memuop", 5 0;
v0x55ac49caa740_0 .var "miscop", 2 0;
v0x55ac49caa820_0 .var "offseten", 0 0;
v0x55ac49caa8e0_0 .var "rmten", 0 0;
v0x55ac49caa9a0_0 .var "tuop", 2 0;
TD_tile_decoder_pkg.extract_instruction_name ;
    %load/vec4 v0x55ac49caa390_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0x55ac49caa1d0_0, 0, 7;
    %load/vec4 v0x55ac49caa1d0_0;
    %cmpi/ne 123, 0, 7;
    %jmp/0xz  T_3.13, 4;
    %pushi/str "unknown_non_tile";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
T_3.13 ;
    %load/vec4 v0x55ac49caa390_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0x55ac49caa9a0_0, 0, 3;
    %load/vec4 v0x55ac49caa9a0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_3.15, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 3;
    %cmp/u;
    %jmp/1 T_3.16, 6;
    %dup/vec4;
    %pushi/vec4 6, 0, 3;
    %cmp/u;
    %jmp/1 T_3.17, 6;
    %dup/vec4;
    %pushi/vec4 4, 0, 3;
    %cmp/u;
    %jmp/1 T_3.18, 6;
    %dup/vec4;
    %pushi/vec4 5, 0, 3;
    %cmp/u;
    %jmp/1 T_3.19, 6;
    %dup/vec4;
    %pushi/vec4 7, 0, 3;
    %cmp/u;
    %jmp/1 T_3.20, 6;
    %pushi/str "unknown_tile";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.22;
T_3.15 ;
    %load/vec4 v0x55ac49caa390_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55ac49caa660_0, 0, 6;
    %load/vec4 v0x55ac49caa390_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0x55ac49caa530_0, 0, 2;
    %load/vec4 v0x55ac49caa660_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 6;
    %cmp/u;
    %jmp/1 T_3.23, 6;
    %dup/vec4;
    %pushi/vec4 8, 0, 6;
    %cmp/u;
    %jmp/1 T_3.24, 6;
    %dup/vec4;
    %pushi/vec4 28, 0, 6;
    %cmp/u;
    %jmp/1 T_3.25, 6;
    %jmp T_3.26;
T_3.23 ;
    %load/vec4 v0x55ac49caa530_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_3.27, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_3.28, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_3.29, 6;
    %jmp T_3.30;
T_3.27 ;
    %load/vec4 v0x55ac49caa450_0;
    %cmpi/e 1, 0, 2;
    %jmp/0xz  T_3.31, 4;
    %load/vec4 v0x55ac49caa390_0;
    %parti/s 1, 47, 7;
    %store/vec4 v0x55ac49caa820_0, 0, 1;
    %load/vec4 v0x55ac49caa390_0;
    %parti/s 1, 52, 7;
    %store/vec4 v0x55ac49caa8e0_0, 0, 1;
    %load/vec4 v0x55ac49caa820_0;
    %nor/r;
    %load/vec4 v0x55ac49caa8e0_0;
    %nor/r;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.33, 8;
    %pushi/str "tld.trii.linear.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.34;
T_3.33 ;
    %load/vec4 v0x55ac49caa820_0;
    %nor/r;
    %load/vec4 v0x55ac49caa8e0_0;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.35, 8;
    %pushi/str "tld.trii.linear.u32.global.remote";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.36;
T_3.35 ;
    %load/vec4 v0x55ac49caa820_0;
    %load/vec4 v0x55ac49caa8e0_0;
    %nor/r;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.37, 8;
    %pushi/str "tld.trir.linear.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.38;
T_3.37 ;
    %pushi/str "tld.trir.linear.u32.global.remote";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
T_3.38 ;
T_3.36 ;
T_3.34 ;
T_3.31 ;
    %jmp T_3.30;
T_3.28 ;
    %pushi/str "tld.trri.stride.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.30;
T_3.29 ;
    %load/vec4 v0x55ac49caa450_0;
    %cmpi/e 2, 0, 2;
    %jmp/0xz  T_3.39, 4;
    %load/vec4 v0x55ac49caa390_0;
    %parti/s 1, 79, 8;
    %store/vec4 v0x55ac49caa820_0, 0, 1;
    %load/vec4 v0x55ac49caa820_0;
    %nor/r;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.41, 8;
    %pushi/str "tld.trvi.asp.index.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.42;
T_3.41 ;
    %pushi/str "tld.trvr.asp.index.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
T_3.42 ;
T_3.39 ;
    %jmp T_3.30;
T_3.30 ;
    %pop/vec4 1;
    %jmp T_3.26;
T_3.24 ;
    %pushi/str "tst.trvi.index.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.26;
T_3.25 ;
    %load/vec4 v0x55ac49caa450_0;
    %cmpi/e 3, 0, 2;
    %jmp/0xz  T_3.43, 4;
    %fork t_1, S_0x55ac49ca9410;
    %jmp t_0;
    .scope S_0x55ac49ca9410;
t_1 ;
    %load/vec4 v0x55ac49caa390_0;
    %parti/s 1, 73, 8;
    %store/vec4 v0x55ac49ca96f0_0, 0, 1;
    %load/vec4 v0x55ac49caa390_0;
    %parti/s 1, 74, 8;
    %store/vec4 v0x55ac49ca9610_0, 0, 1;
    %load/vec4 v0x55ac49ca96f0_0;
    %nor/r;
    %load/vec4 v0x55ac49ca9610_0;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.45, 8;
    %pushi/str "tacp.rvv.asp2.dsttm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.46;
T_3.45 ;
    %load/vec4 v0x55ac49ca96f0_0;
    %load/vec4 v0x55ac49ca9610_0;
    %nor/r;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.47, 8;
    %pushi/str "tacp.vvr.asp2.srctm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.48;
T_3.47 ;
    %pushi/str "tacp.rvrv.asp2.mbar.dsttm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
T_3.48 ;
T_3.46 ;
    %end;
    .scope S_0x55ac49ca9230;
t_0 %join;
T_3.43 ;
    %jmp T_3.26;
T_3.26 ;
    %pop/vec4 1;
    %jmp T_3.22;
T_3.16 ;
    %pushi/str "tmma.ttt";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.22;
T_3.17 ;
    %load/vec4 v0x55ac49caa450_0;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_3.49, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_3.50, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_3.51, 6;
    %pushi/str "unknown_tuop110";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.53;
T_3.49 ;
    %pushi/str "tld_64bit";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.53;
T_3.50 ;
    %pushi/str "tld_96bit";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.53;
T_3.51 ;
    %pushi/str "tacp_128bit";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.53;
T_3.53 ;
    %pop/vec4 1;
    %jmp T_3.22;
T_3.18 ;
    %fork t_3, S_0x55ac49ca97b0;
    %jmp t_2;
    .scope S_0x55ac49ca97b0;
t_3 ;
    %load/vec4 v0x55ac49caa390_0;
    %parti/s 2, 30, 6;
    %store/vec4 v0x55ac49ca99b0_0, 0, 2;
    %load/vec4 v0x55ac49ca99b0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_3.54, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_3.55, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_3.56, 6;
    %pushi/str "unknown_csr";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.58;
T_3.54 ;
    %pushi/str "tcsrw.i";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.58;
T_3.55 ;
    %pushi/str "tcsrr.r";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.58;
T_3.56 ;
    %pushi/str "tcsrw.r";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.58;
T_3.58 ;
    %pop/vec4 1;
    %end;
    .scope S_0x55ac49ca9230;
t_2 %join;
    %jmp T_3.22;
T_3.19 ;
    %fork t_5, S_0x55ac49ca9a90;
    %jmp t_4;
    .scope S_0x55ac49ca9a90;
t_5 ;
    %load/vec4 v0x55ac49caa390_0;
    %parti/s 3, 23, 6;
    %store/vec4 v0x55ac49ca9c70_0, 0, 3;
    %load/vec4 v0x55ac49caa390_0;
    %parti/s 3, 28, 6;
    %store/vec4 v0x55ac49ca9d50_0, 0, 3;
    %load/vec4 v0x55ac49ca9c70_0;
    %pushi/vec4 1, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x55ac49ca9d50_0;
    %pushi/vec4 7, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.59, 8;
    %load/vec4 v0x55ac49caa390_0;
    %parti/s 1, 26, 6;
    %cmpi/e 1, 0, 1;
    %jmp/0xz  T_3.61, 4;
    %pushi/str "twait.mem";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.62;
T_3.61 ;
    %pushi/str "twait";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
T_3.62 ;
    %jmp T_3.60;
T_3.59 ;
    %pushi/str "unknown_sync";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
T_3.60 ;
    %end;
    .scope S_0x55ac49ca9230;
t_4 %join;
    %jmp T_3.22;
T_3.20 ;
    %fork t_7, S_0x55ac49ca9e30;
    %jmp t_6;
    .scope S_0x55ac49ca9e30;
t_7 ;
    %load/vec4 v0x55ac49caa390_0;
    %parti/s 3, 26, 6;
    %store/vec4 v0x55ac49caa0f0_0, 0, 3;
    %load/vec4 v0x55ac49caa390_0;
    %parti/s 1, 31, 6;
    %store/vec4 v0x55ac49caa010_0, 0, 1;
    %load/vec4 v0x55ac49caa010_0;
    %cmpi/e 1, 0, 1;
    %jmp/0xz  T_3.63, 4;
    %load/vec4 v0x55ac49caa0f0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_3.65, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 3;
    %cmp/u;
    %jmp/1 T_3.66, 6;
    %pushi/str "unknown_ace_misc";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.68;
T_3.65 ;
    %pushi/str "ace_bsync";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.68;
T_3.66 ;
    %pushi/str "ace_nbsync";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %jmp T_3.68;
T_3.68 ;
    %pop/vec4 1;
    %jmp T_3.64;
T_3.63 ;
    %pushi/str "unknown_ace";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
T_3.64 ;
    %end;
    .scope S_0x55ac49ca9230;
t_6 %join;
    %jmp T_3.22;
T_3.22 ;
    %pop/vec4 1;
    %pushi/str "unknown";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55ac49ca9230;
    %end;
S_0x55ac49ca9410 .scope autobegin, "$unm_blk_19" "$unm_blk_19" 3 234, 3 234 0, S_0x55ac49ca9230;
 .timescale 0 0;
v0x55ac49ca9610_0 .var "dsttm", 0 0;
v0x55ac49ca96f0_0 .var "srctm", 0 0;
S_0x55ac49ca97b0 .scope autobegin, "$unm_blk_21" "$unm_blk_21" 3 264, 3 264 0, S_0x55ac49ca9230;
 .timescale 0 0;
v0x55ac49ca99b0_0 .var "rw", 1 0;
S_0x55ac49ca9a90 .scope autobegin, "$unm_blk_22" "$unm_blk_22" 3 274, 3 274 0, S_0x55ac49ca9230;
 .timescale 0 0;
v0x55ac49ca9c70_0 .var "ctrluop", 2 0;
v0x55ac49ca9d50_0 .var "waitop", 2 0;
S_0x55ac49ca9e30 .scope autobegin, "$unm_blk_25" "$unm_blk_25" 3 292, 3 292 0, S_0x55ac49ca9230;
 .timescale 0 0;
v0x55ac49caa010_0 .var "ace_misc_en", 0 0;
v0x55ac49caa0f0_0 .var "miscop", 2 0;
S_0x55ac49caaa80 .scope autofunction.str, "format_operands" "format_operands" 3 314, 3 314 0, S_0x55ac49c38c40;
 .timescale 0 0;
; Variable format_operands is string return value of scope S_0x55ac49caaa80
v0x55ac49cac600_0 .var/str "instr_name";
v0x55ac49cac6c0_0 .var "instruction_data", 127 0;
v0x55ac49cac780_0 .var "length", 1 0;
v0x55ac49cac860_0 .var/str "operands";
v0x55ac49cac970_0 .var "rs1", 4 0;
v0x55ac49caca50_0 .var "rs2", 4 0;
v0x55ac49cacb30_0 .var "td", 7 0;
TD_tile_decoder_pkg.format_operands ;
    %pushi/str "";
    %store/str v0x55ac49cac860_0;
    %load/vec4 v0x55ac49cac780_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_4.69, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_4.70, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_4.71, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_4.72, 6;
    %jmp T_4.73;
T_4.69 ;
    %load/str v0x55ac49cac600_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 4, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tcsr";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.74, 8;
T_4.74 ;
    %jmp T_4.73;
T_4.70 ;
    %fork t_9, S_0x55ac49caac60;
    %jmp t_8;
    .scope S_0x55ac49caac60;
t_9 ;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0x55ac49caaf60_0, 0, 3;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55ac49caae60_0, 0, 6;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 3, 44, 7;
    %store/vec4 v0x55ac49cab040_0, 0, 3;
    %load/vec4 v0x55ac49caaf60_0;
    %pushi/vec4 6, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x55ac49caae60_0;
    %pushi/vec4 1, 0, 6;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %load/vec4 v0x55ac49cab040_0;
    %pushi/vec4 1, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.76, 8;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 8, 55, 7;
    %store/vec4 v0x55ac49cacb30_0, 0, 8;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 5, 47, 7;
    %store/vec4 v0x55ac49cac970_0, 0, 5;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 5, 20, 6;
    %store/vec4 v0x55ac49caca50_0, 0, 5;
    %jmp T_4.77;
T_4.76 ;
    %load/str v0x55ac49cac600_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 3, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tld";
    %cmp/str;
    %flag_get/vec4 4;
    %load/str v0x55ac49cac600_0;
    %ix/load 4, 4, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 3, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "trr";
    %cmp/str;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.78, 8;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 8, 55, 7;
    %store/vec4 v0x55ac49cacb30_0, 0, 8;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 5, 47, 7;
    %store/vec4 v0x55ac49cac970_0, 0, 5;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 5, 20, 6;
    %store/vec4 v0x55ac49caca50_0, 0, 5;
    %jmp T_4.79;
T_4.78 ;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 8, 32, 7;
    %store/vec4 v0x55ac49cacb30_0, 0, 8;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 5, 48, 7;
    %store/vec4 v0x55ac49cac970_0, 0, 5;
T_4.79 ;
T_4.77 ;
    %end;
    .scope S_0x55ac49caaa80;
t_8 %join;
    %jmp T_4.73;
T_4.71 ;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 8, 48, 7;
    %store/vec4 v0x55ac49cacb30_0, 0, 8;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 5, 43, 7;
    %store/vec4 v0x55ac49cac970_0, 0, 5;
    %jmp T_4.73;
T_4.72 ;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 5, 47, 7;
    %store/vec4 v0x55ac49cac970_0, 0, 5;
    %jmp T_4.73;
T_4.73 ;
    %pop/vec4 1;
    %load/vec4 v0x55ac49cac780_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_4.80, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_4.81, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_4.82, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_4.83, 6;
    %jmp T_4.84;
T_4.80 ;
    %load/str v0x55ac49cac600_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 4, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tcsr";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.85, 8;
    %fork t_11, S_0x55ac49cab100;
    %jmp t_10;
    .scope S_0x55ac49cab100;
t_11 ;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 2, 30, 6;
    %store/vec4 v0x55ac49cab580_0, 0, 2;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 9, 20, 6;
    %store/vec4 v0x55ac49cab300_0, 0, 9;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 5, 7, 4;
    %store/vec4 v0x55ac49cab3e0_0, 0, 5;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x55ac49cab4c0_0, 0, 5;
    %load/vec4 v0x55ac49cab580_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_4.87, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_4.88, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_4.89, 6;
    %pushi/str "unknown";
    %store/str v0x55ac49cac860_0;
    %jmp T_4.91;
T_4.87 ;
    %vpi_call/w 3 383 "$sformat", v0x55ac49cac860_0, "0x%0x", v0x55ac49cab4c0_0 {0 0 0};
    %jmp T_4.91;
T_4.88 ;
    %vpi_call/w 3 386 "$sformat", v0x55ac49cac860_0, "x%0d", v0x55ac49cab3e0_0 {0 0 0};
    %jmp T_4.91;
T_4.89 ;
    %vpi_call/w 3 389 "$sformat", v0x55ac49cac860_0, "x%0d", v0x55ac49cab4c0_0 {0 0 0};
    %jmp T_4.91;
T_4.91 ;
    %pop/vec4 1;
    %end;
    .scope S_0x55ac49caaa80;
t_10 %join;
    %jmp T_4.86;
T_4.85 ;
    %load/str v0x55ac49cac600_0;
    %pushi/str "twait";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %load/str v0x55ac49cac600_0;
    %pushi/str "twait.mem";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %jmp/0xz  T_4.92, 9;
    %pushi/str "";
    %store/str v0x55ac49cac860_0;
    %jmp T_4.93;
T_4.92 ;
    %load/str v0x55ac49cac600_0;
    %pushi/str "twait.i.load.global";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %load/str v0x55ac49cac600_0;
    %pushi/str "twait.i.load.share";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %load/str v0x55ac49cac600_0;
    %pushi/str "twait.i.store.global";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %flag_or 8, 9;
    %load/str v0x55ac49cac600_0;
    %pushi/str "twait.i.store.share";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %jmp/0xz  T_4.94, 9;
    %fork t_13, S_0x55ac49cab660;
    %jmp t_12;
    .scope S_0x55ac49cab660;
t_13 ;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 8, 15, 5;
    %store/vec4 v0x55ac49cab870_0, 0, 8;
    %vpi_call/w 3 400 "$sformat", v0x55ac49cac860_0, "%0d", v0x55ac49cab870_0 {0 0 0};
    %end;
    .scope S_0x55ac49caaa80;
t_12 %join;
    %jmp T_4.95;
T_4.94 ;
    %load/str v0x55ac49cac600_0;
    %pushi/str "twait.r.load.global";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %load/str v0x55ac49cac600_0;
    %pushi/str "twait.r.load.share";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %load/str v0x55ac49cac600_0;
    %pushi/str "twait.r.store.global";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %flag_or 8, 9;
    %load/str v0x55ac49cac600_0;
    %pushi/str "twait.r.store.share";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %load/str v0x55ac49cac600_0;
    %pushi/str "twait.r.tacp_cg";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %flag_or 8, 9;
    %load/str v0x55ac49cac600_0;
    %pushi/str "twait.r.rmtfence";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %jmp/0xz  T_4.96, 9;
    %fork t_15, S_0x55ac49cab950;
    %jmp t_14;
    .scope S_0x55ac49cab950;
t_15 ;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x55ac49cabb30_0, 0, 5;
    %vpi_call/w 3 406 "$sformat", v0x55ac49cac860_0, "x%0d", v0x55ac49cabb30_0 {0 0 0};
    %end;
    .scope S_0x55ac49caaa80;
t_14 %join;
    %jmp T_4.97;
T_4.96 ;
    %load/str v0x55ac49cac600_0;
    %pushi/str "tsync.i";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.98, 8;
    %fork t_17, S_0x55ac49cabc30;
    %jmp t_16;
    .scope S_0x55ac49cabc30;
t_17 ;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x55ac49cabe60_0, 0, 5;
    %vpi_call/w 3 410 "$sformat", v0x55ac49cac860_0, "%0d", v0x55ac49cabe60_0 {0 0 0};
    %end;
    .scope S_0x55ac49caaa80;
t_16 %join;
    %jmp T_4.99;
T_4.98 ;
    %load/str v0x55ac49cac600_0;
    %pushi/str "tkill.r";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.100, 8;
    %fork t_19, S_0x55ac49cabf60;
    %jmp t_18;
    .scope S_0x55ac49cabf60;
t_19 ;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x55ac49cac140_0, 0, 5;
    %vpi_call/w 3 414 "$sformat", v0x55ac49cac860_0, "x%0d", v0x55ac49cac140_0 {0 0 0};
    %end;
    .scope S_0x55ac49caaa80;
t_18 %join;
    %jmp T_4.101;
T_4.100 ;
    %load/str v0x55ac49cac600_0;
    %pushi/str "ace_bsync";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %load/str v0x55ac49cac600_0;
    %pushi/str "ace_nbsync";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %jmp/0xz  T_4.102, 9;
    %fork t_21, S_0x55ac49cac240;
    %jmp t_20;
    .scope S_0x55ac49cac240;
t_21 ;
    %load/vec4 v0x55ac49cac6c0_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x55ac49cac420_0, 0, 5;
    %vpi_call/w 3 418 "$sformat", v0x55ac49cac860_0, "x%0d", v0x55ac49cac420_0 {0 0 0};
    %end;
    .scope S_0x55ac49caaa80;
t_20 %join;
    %jmp T_4.103;
T_4.102 ;
    %pushi/str "0";
    %store/str v0x55ac49cac860_0;
T_4.103 ;
T_4.101 ;
T_4.99 ;
T_4.97 ;
T_4.95 ;
T_4.93 ;
T_4.86 ;
    %jmp T_4.84;
T_4.81 ;
    %vpi_call/w 3 427 "$sformat", v0x55ac49cac860_0, "t%0d, (x%0d)", v0x55ac49cacb30_0, v0x55ac49cac970_0 {0 0 0};
    %jmp T_4.84;
T_4.82 ;
    %vpi_call/w 3 432 "$sformat", v0x55ac49cac860_0, "t%0d, (x%0d)", v0x55ac49cacb30_0, v0x55ac49cac970_0 {0 0 0};
    %jmp T_4.84;
T_4.83 ;
    %vpi_call/w 3 437 "$sformat", v0x55ac49cac860_0, "t%0d, t%0d, x%0d", v0x55ac49cacb30_0, v0x55ac49cacb30_0, v0x55ac49cac970_0 {0 0 0};
    %jmp T_4.84;
T_4.84 ;
    %pop/vec4 1;
    %load/str v0x55ac49cac860_0;
    %ret/str 0; Assign to format_operands
    %disable S_0x55ac49caaa80;
    %end;
S_0x55ac49caac60 .scope autobegin, "$unm_blk_29" "$unm_blk_29" 3 333, 3 333 0, S_0x55ac49caaa80;
 .timescale 0 0;
v0x55ac49caae60_0 .var "memuop_field", 5 0;
v0x55ac49caaf60_0 .var "tuop_first", 2 0;
v0x55ac49cab040_0 .var "tuop_second", 2 0;
S_0x55ac49cab100 .scope autobegin, "$unm_blk_36" "$unm_blk_36" 3 374, 3 374 0, S_0x55ac49caaa80;
 .timescale 0 0;
v0x55ac49cab300_0 .var "csr_addr", 8 0;
v0x55ac49cab3e0_0 .var "rd", 4 0;
v0x55ac49cab4c0_0 .var "rs1_or_imm", 4 0;
v0x55ac49cab580_0 .var "rw", 1 0;
S_0x55ac49cab660 .scope autobegin, "$unm_blk_41" "$unm_blk_41" 3 397, 3 397 0, S_0x55ac49caaa80;
 .timescale 0 0;
v0x55ac49cab870_0 .var "cnt", 7 0;
S_0x55ac49cab950 .scope autobegin, "$unm_blk_42" "$unm_blk_42" 3 403, 3 403 0, S_0x55ac49caaa80;
 .timescale 0 0;
v0x55ac49cabb30_0 .var "rs1", 4 0;
S_0x55ac49cabc30 .scope autobegin, "$unm_blk_43" "$unm_blk_43" 3 407, 3 407 0, S_0x55ac49caaa80;
 .timescale 0 0;
v0x55ac49cabe60_0 .var "sync_id", 4 0;
S_0x55ac49cabf60 .scope autobegin, "$unm_blk_44" "$unm_blk_44" 3 411, 3 411 0, S_0x55ac49caaa80;
 .timescale 0 0;
v0x55ac49cac140_0 .var "rs1", 4 0;
S_0x55ac49cac240 .scope autobegin, "$unm_blk_45" "$unm_blk_45" 3 415, 3 415 0, S_0x55ac49caaa80;
 .timescale 0 0;
v0x55ac49cac420_0 .var "sync_id", 4 0;
S_0x55ac49cacc10 .scope autofunction.vec2.u32, "get_instruction_bits" "get_instruction_bits" 3 167, 3 167 0, S_0x55ac49c38c40;
 .timescale 0 0;
; Variable get_instruction_bits is bool return value of scope S_0x55ac49cacc10
v0x55ac49cacef0_0 .var "length", 1 0;
TD_tile_decoder_pkg.get_instruction_bits ;
    %load/vec4 v0x55ac49cacef0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_5.104, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_5.105, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_5.106, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_5.107, 6;
    %pushi/vec4 32, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x55ac49cacc10;
    %jmp T_5.109;
T_5.104 ;
    %pushi/vec4 32, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x55ac49cacc10;
    %jmp T_5.109;
T_5.105 ;
    %pushi/vec4 64, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x55ac49cacc10;
    %jmp T_5.109;
T_5.106 ;
    %pushi/vec4 96, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x55ac49cacc10;
    %jmp T_5.109;
T_5.107 ;
    %pushi/vec4 128, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x55ac49cacc10;
    %jmp T_5.109;
T_5.109 ;
    %pop/vec4 1;
    %end;
S_0x55ac49cacfd0 .scope autofunction.vec4.s2, "get_instruction_length" "get_instruction_length" 3 40, 3 40 0, S_0x55ac49c38c40;
 .timescale 0 0;
v0x55ac49cad1b0_0 .var "ace_op", 6 0;
v0x55ac49cad2b0_0 .var "first_word", 31 0;
; Variable get_instruction_length is vec4 return value of scope S_0x55ac49cacfd0
v0x55ac49cad450_0 .var "lsuop", 1 0;
v0x55ac49cad530_0 .var "memuop", 5 0;
v0x55ac49cad660_0 .var "tuop", 2 0;
TD_tile_decoder_pkg.get_instruction_length ;
    %alloc S_0x55ac49ca8e90;
    %load/vec4 v0x55ac49cad2b0_0;
    %store/vec4 v0x55ac49ca9150_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.extract_ace_op, S_0x55ac49ca8e90;
    %free S_0x55ac49ca8e90;
    %store/vec4 v0x55ac49cad1b0_0, 0, 7;
    %load/vec4 v0x55ac49cad1b0_0;
    %cmpi/ne 123, 0, 7;
    %jmp/0xz  T_6.110, 4;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
T_6.110 ;
    %load/vec4 v0x55ac49cad2b0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0x55ac49cad660_0, 0, 3;
    %load/vec4 v0x55ac49cad660_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_6.112, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 3;
    %cmp/u;
    %jmp/1 T_6.113, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 3;
    %cmp/u;
    %jmp/1 T_6.114, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 3;
    %cmp/u;
    %jmp/1 T_6.115, 6;
    %dup/vec4;
    %pushi/vec4 4, 0, 3;
    %cmp/u;
    %jmp/1 T_6.116, 6;
    %dup/vec4;
    %pushi/vec4 5, 0, 3;
    %cmp/u;
    %jmp/1 T_6.117, 6;
    %dup/vec4;
    %pushi/vec4 6, 0, 3;
    %cmp/u;
    %jmp/1 T_6.118, 6;
    %dup/vec4;
    %pushi/vec4 7, 0, 3;
    %cmp/u;
    %jmp/1 T_6.119, 6;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.121;
T_6.112 ;
    %load/vec4 v0x55ac49cad2b0_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55ac49cad530_0, 0, 6;
    %load/vec4 v0x55ac49cad2b0_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0x55ac49cad450_0, 0, 2;
    %load/vec4 v0x55ac49cad450_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_6.122, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_6.123, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_6.124, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_6.125, 6;
    %jmp T_6.126;
T_6.122 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.126;
T_6.123 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.126;
T_6.124 ;
    %pushi/vec4 2, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.126;
T_6.125 ;
    %load/vec4 v0x55ac49cad530_0;
    %cmpi/e 28, 0, 6;
    %jmp/0xz  T_6.127, 4;
    %pushi/vec4 3, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.128;
T_6.127 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
T_6.128 ;
    %jmp T_6.126;
T_6.126 ;
    %pop/vec4 1;
    %jmp T_6.121;
T_6.113 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.121;
T_6.114 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.121;
T_6.115 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.121;
T_6.116 ;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.121;
T_6.117 ;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.121;
T_6.118 ;
    %load/vec4 v0x55ac49cad2b0_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0x55ac49cad450_0, 0, 2;
    %load/vec4 v0x55ac49cad2b0_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55ac49cad530_0, 0, 6;
    %load/vec4 v0x55ac49cad530_0;
    %cmpi/e 1, 0, 6;
    %jmp/0xz  T_6.129, 4;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.130;
T_6.129 ;
    %load/vec4 v0x55ac49cad530_0;
    %cmpi/e 0, 0, 6;
    %jmp/0xz  T_6.131, 4;
    %load/vec4 v0x55ac49cad450_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_6.133, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_6.134, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_6.135, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_6.136, 6;
    %jmp T_6.137;
T_6.133 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.137;
T_6.134 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.137;
T_6.135 ;
    %pushi/vec4 2, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.137;
T_6.136 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.137;
T_6.137 ;
    %pop/vec4 1;
    %jmp T_6.132;
T_6.131 ;
    %load/vec4 v0x55ac49cad530_0;
    %cmpi/e 28, 0, 6;
    %jmp/0xz  T_6.138, 4;
    %pushi/vec4 3, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.139;
T_6.138 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
T_6.139 ;
T_6.132 ;
T_6.130 ;
    %jmp T_6.121;
T_6.119 ;
    %load/vec4 v0x55ac49cad2b0_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55ac49cad530_0, 0, 6;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55ac49cacfd0;
    %jmp T_6.121;
T_6.121 ;
    %pop/vec4 1;
    %end;
S_0x55ac49cad740 .scope autofunction.vec4.s135, "init_collector" "init_collector" 3 121, 3 121 0, S_0x55ac49c38c40;
 .timescale 0 0;
v0x55ac49cad920_0 .var "collector", 134 0;
v0x55ac49cada20_0 .var "first_word", 31 0;
; Variable init_collector is vec4 return value of scope S_0x55ac49cad740
TD_tile_decoder_pkg.init_collector ;
    %pushi/vec4 0, 0, 128;
    %ix/load 4, 7, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55ac49cad920_0, 4, 128;
    %load/vec4 v0x55ac49cada20_0;
    %ix/load 4, 7, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55ac49cad920_0, 4, 32;
    %pushi/vec4 1, 0, 3;
    %ix/load 4, 4, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55ac49cad920_0, 4, 3;
    %alloc S_0x55ac49cacfd0;
    %load/vec4 v0x55ac49cada20_0;
    %store/vec4 v0x55ac49cad2b0_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.get_instruction_length, S_0x55ac49cacfd0;
    %free S_0x55ac49cacfd0;
    %ix/load 4, 2, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55ac49cad920_0, 4, 2;
    %alloc S_0x55ac49cadbc0;
    %load/vec4 v0x55ac49cada20_0;
    %store/vec4 v0x55ac49cadf30_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.is_tile_instruction, S_0x55ac49cadbc0;
    %free S_0x55ac49cadbc0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55ac49cad920_0, 4, 1;
    %load/vec4 v0x55ac49cad920_0;
    %parti/u 2, 2, 32;
    %pushi/vec4 0, 0, 2;
    %cmp/e;
    %flag_get/vec4 4;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55ac49cad920_0, 4, 1;
    %load/vec4 v0x55ac49cad920_0;
    %ret/vec4 0, 0, 135;  Assign to init_collector (store_vec4_to_lval)
    %disable S_0x55ac49cad740;
    %end;
S_0x55ac49cadbc0 .scope autofunction.vec4.s1, "is_tile_instruction" "is_tile_instruction" 3 33, 3 33 0, S_0x55ac49c38c40;
 .timescale 0 0;
v0x55ac49cade30_0 .var "ace_op", 6 0;
v0x55ac49cadf30_0 .var "first_word", 31 0;
; Variable is_tile_instruction is vec4 return value of scope S_0x55ac49cadbc0
TD_tile_decoder_pkg.is_tile_instruction ;
    %alloc S_0x55ac49ca8e90;
    %load/vec4 v0x55ac49cadf30_0;
    %store/vec4 v0x55ac49ca9150_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.extract_ace_op, S_0x55ac49ca8e90;
    %free S_0x55ac49ca8e90;
    %store/vec4 v0x55ac49cade30_0, 0, 7;
    %load/vec4 v0x55ac49cade30_0;
    %pushi/vec4 123, 0, 7;
    %cmp/e;
    %flag_get/vec4 4;
    %ret/vec4 0, 0, 1;  Assign to is_tile_instruction (store_vec4_to_lval)
    %disable S_0x55ac49cadbc0;
    %end;
S_0x55ac49c3e080 .scope module, "test_tacp_fix" "test_tacp_fix" 4 8;
 .timescale 0 0;
v0x55ac49cae0e0_0 .var "collector", 134 0;
v0x55ac49cae1e0_0 .var/str "result";
v0x55ac49cae2a0_0 .var "test_64bit_0", 31 0;
v0x55ac49cae390_0 .var "test_64bit_1", 31 0;
v0x55ac49cae470 .array "words", 0 3, 31 0;
S_0x55ac49c3e210 .scope module, "tile_instruction_decoder_example" "tile_instruction_decoder_example" 3 467;
 .timescale 0 0;
S_0x55ac49cae530 .scope begin, "$unm_blk_50" "$unm_blk_50" 3 470, 3 470 0, S_0x55ac49c3e210;
 .timescale 0 0;
v0x55ac49cae730_0 .var "collector", 134 0;
v0x55ac49cae830_0 .var/str "disasm_result";
v0x55ac49cae8f0_0 .var "test_word", 31 0;
    .scope S_0x55ac49c3e080;
T_9 ;
    %vpi_call/w 4 16 "$display", "=== Testing TACP Instruction Fix ===" {0 0 0};
    %pushi/vec4 940499195, 0, 32;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %store/vec4a v0x55ac49cae470, 4, 0;
    %pushi/vec4 2382692731, 0, 32;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4a v0x55ac49cae470, 4, 0;
    %pushi/vec4 0, 0, 32;
    %ix/load 4, 2, 0;
    %flag_set/imm 4, 0;
    %store/vec4a v0x55ac49cae470, 4, 0;
    %pushi/vec4 0, 0, 32;
    %ix/load 4, 3, 0;
    %flag_set/imm 4, 0;
    %store/vec4a v0x55ac49cae470, 4, 0;
    %vpi_call/w 4 24 "$display", "\012Testing 128-bit tacp instruction:" {0 0 0};
    %vpi_call/w 4 25 "$display", "Word 0: 0x%08x", &A<v0x55ac49cae470, 0> {0 0 0};
    %vpi_call/w 4 26 "$display", "Word 1: 0x%08x", &A<v0x55ac49cae470, 1> {0 0 0};
    %vpi_call/w 4 27 "$display", "Word 2: 0x%08x", &A<v0x55ac49cae470, 2> {0 0 0};
    %vpi_call/w 4 28 "$display", "Word 3: 0x%08x", &A<v0x55ac49cae470, 3> {0 0 0};
    %alloc S_0x55ac49cad740;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %load/vec4a v0x55ac49cae470, 4;
    %store/vec4 v0x55ac49cada20_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x55ac49cad740;
    %free S_0x55ac49cad740;
    %store/vec4 v0x55ac49cae0e0_0, 0, 135;
    %vpi_call/w 4 32 "$display", "\012After init:" {0 0 0};
    %vpi_call/w 4 33 "$display", "  collected_words: %d", &PV<v0x55ac49cae0e0_0, 4, 3> {0 0 0};
    %load/vec4 v0x55ac49cae0e0_0;
    %parti/u 2, 2, 32;
    %cmpi/e 3, 0, 2;
    %flag_mov 8, 4;
    %jmp/0 T_9.0, 8;
    %pushi/vec4 1229869908, 0, 32; draw_string_vec4
    %pushi/vec4 1381970226, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 943868244, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_9.1, 8;
T_9.0 ; End of true expr.
    %pushi/vec4 0, 0, 32; draw_string_vec4
    %pushi/vec4 79, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %pushi/vec4 1414022482, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_9.1, 8;
 ; End of false expr.
    %blend;
T_9.1;
    %vpi_call/w 4 34 "$display", "  expected_length: %s", S<0,vec4,u96> {1 0 0};
    %load/vec4 v0x55ac49cae0e0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_9.2, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_9.3, 8;
T_9.2 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_9.3, 8;
 ; End of false expr.
    %blend;
T_9.3;
    %vpi_call/w 4 36 "$display", "  is_complete: %s", S<0,vec4,u24> {1 0 0};
    %alloc S_0x55ac49c3e3a0;
    %load/vec4 v0x55ac49cae0e0_0;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %load/vec4a v0x55ac49cae470, 4;
    %store/vec4 v0x55ac49ca86c0_0, 0, 32;
    %store/vec4 v0x55ac49ca8520_0, 0, 135;
    %callf/vec4 TD_tile_decoder_pkg.add_word_to_collector, S_0x55ac49c3e3a0;
    %free S_0x55ac49c3e3a0;
    %store/vec4 v0x55ac49cae0e0_0, 0, 135;
    %vpi_call/w 4 40 "$display", "\012After adding word 1:" {0 0 0};
    %vpi_call/w 4 41 "$display", "  collected_words: %d", &PV<v0x55ac49cae0e0_0, 4, 3> {0 0 0};
    %load/vec4 v0x55ac49cae0e0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_9.4, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_9.5, 8;
T_9.4 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_9.5, 8;
 ; End of false expr.
    %blend;
T_9.5;
    %vpi_call/w 4 42 "$display", "  is_complete: %s", S<0,vec4,u24> {1 0 0};
    %alloc S_0x55ac49c3e3a0;
    %load/vec4 v0x55ac49cae0e0_0;
    %ix/load 4, 2, 0;
    %flag_set/imm 4, 0;
    %load/vec4a v0x55ac49cae470, 4;
    %store/vec4 v0x55ac49ca86c0_0, 0, 32;
    %store/vec4 v0x55ac49ca8520_0, 0, 135;
    %callf/vec4 TD_tile_decoder_pkg.add_word_to_collector, S_0x55ac49c3e3a0;
    %free S_0x55ac49c3e3a0;
    %store/vec4 v0x55ac49cae0e0_0, 0, 135;
    %vpi_call/w 4 45 "$display", "\012After adding word 2:" {0 0 0};
    %vpi_call/w 4 46 "$display", "  collected_words: %d", &PV<v0x55ac49cae0e0_0, 4, 3> {0 0 0};
    %load/vec4 v0x55ac49cae0e0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_9.6, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_9.7, 8;
T_9.6 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_9.7, 8;
 ; End of false expr.
    %blend;
T_9.7;
    %vpi_call/w 4 47 "$display", "  is_complete: %s", S<0,vec4,u24> {1 0 0};
    %alloc S_0x55ac49c3e3a0;
    %load/vec4 v0x55ac49cae0e0_0;
    %ix/load 4, 3, 0;
    %flag_set/imm 4, 0;
    %load/vec4a v0x55ac49cae470, 4;
    %store/vec4 v0x55ac49ca86c0_0, 0, 32;
    %store/vec4 v0x55ac49ca8520_0, 0, 135;
    %callf/vec4 TD_tile_decoder_pkg.add_word_to_collector, S_0x55ac49c3e3a0;
    %free S_0x55ac49c3e3a0;
    %store/vec4 v0x55ac49cae0e0_0, 0, 135;
    %vpi_call/w 4 50 "$display", "\012After adding word 3:" {0 0 0};
    %vpi_call/w 4 51 "$display", "  collected_words: %d", &PV<v0x55ac49cae0e0_0, 4, 3> {0 0 0};
    %load/vec4 v0x55ac49cae0e0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_9.8, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_9.9, 8;
T_9.8 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_9.9, 8;
 ; End of false expr.
    %blend;
T_9.9;
    %vpi_call/w 4 52 "$display", "  is_complete: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x55ac49cae0e0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.10, 8;
    %alloc S_0x55ac49ca87a0;
    %load/vec4 v0x55ac49cae0e0_0;
    %parti/u 128, 7, 32;
    %load/vec4 v0x55ac49cae0e0_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x55ac49ca8be0_0, 0, 2;
    %store/vec4 v0x55ac49ca8b20_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x55ac49ca87a0;
    %free S_0x55ac49ca87a0;
    %store/str v0x55ac49cae1e0_0;
    %vpi_call/w 4 57 "$display", "  Disassembly: %s", v0x55ac49cae1e0_0 {0 0 0};
T_9.10 ;
    %load/vec4 v0x55ac49cae0e0_0;
    %parti/u 1, 1, 32;
    %load/vec4 v0x55ac49cae0e0_0;
    %parti/u 3, 4, 32;
    %pad/u 32;
    %pushi/vec4 4, 0, 32;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.12, 8;
    %vpi_call/w 4 62 "$display", "\012\342\234\223 SUCCESS: 128-bit instruction collection works correctly!" {0 0 0};
    %vpi_call/w 4 63 "$display", "  Final collected_words: %d", &PV<v0x55ac49cae0e0_0, 4, 3> {0 0 0};
    %load/vec4 v0x55ac49cae0e0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_9.14, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_9.15, 8;
T_9.14 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_9.15, 8;
 ; End of false expr.
    %blend;
T_9.15;
    %vpi_call/w 4 64 "$display", "  Final is_complete: %s", S<0,vec4,u24> {1 0 0};
    %jmp T_9.13;
T_9.12 ;
    %vpi_call/w 4 66 "$display", "\012\342\234\227 FAILURE: 128-bit instruction collection still broken" {0 0 0};
    %vpi_call/w 4 67 "$display", "  Final collected_words: %d", &PV<v0x55ac49cae0e0_0, 4, 3> {0 0 0};
    %load/vec4 v0x55ac49cae0e0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_9.16, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_9.17, 8;
T_9.16 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_9.17, 8;
 ; End of false expr.
    %blend;
T_9.17;
    %vpi_call/w 4 68 "$display", "  Final is_complete: %s", S<0,vec4,u24> {1 0 0};
T_9.13 ;
    %vpi_call/w 4 72 "$display", "\012=== Testing Edge Cases ===" {0 0 0};
    %pushi/vec4 24699, 0, 32;
    %store/vec4 v0x55ac49cae2a0_0, 0, 32;
    %pushi/vec4 2147483771, 0, 32;
    %store/vec4 v0x55ac49cae390_0, 0, 32;
    %alloc S_0x55ac49cad740;
    %load/vec4 v0x55ac49cae2a0_0;
    %store/vec4 v0x55ac49cada20_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x55ac49cad740;
    %free S_0x55ac49cad740;
    %store/vec4 v0x55ac49cae0e0_0, 0, 135;
    %vpi_call/w 4 79 "$display", "\01264-bit instruction test:" {0 0 0};
    %load/vec4 v0x55ac49cae0e0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_9.18, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_9.19, 8;
T_9.18 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_9.19, 8;
 ; End of false expr.
    %blend;
T_9.19;
    %vpi_call/w 4 80 "$display", "  After init: collected_words=%d, is_complete=%s", &PV<v0x55ac49cae0e0_0, 4, 3>, S<0,vec4,u24> {1 0 0};
    %alloc S_0x55ac49c3e3a0;
    %load/vec4 v0x55ac49cae0e0_0;
    %load/vec4 v0x55ac49cae390_0;
    %store/vec4 v0x55ac49ca86c0_0, 0, 32;
    %store/vec4 v0x55ac49ca8520_0, 0, 135;
    %callf/vec4 TD_tile_decoder_pkg.add_word_to_collector, S_0x55ac49c3e3a0;
    %free S_0x55ac49c3e3a0;
    %store/vec4 v0x55ac49cae0e0_0, 0, 135;
    %load/vec4 v0x55ac49cae0e0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_9.20, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_9.21, 8;
T_9.20 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_9.21, 8;
 ; End of false expr.
    %blend;
T_9.21;
    %vpi_call/w 4 84 "$display", "  After word 2: collected_words=%d, is_complete=%s", &PV<v0x55ac49cae0e0_0, 4, 3>, S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x55ac49cae0e0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.22, 8;
    %vpi_call/w 4 88 "$display", "\342\234\223 64-bit instruction collection still works" {0 0 0};
    %jmp T_9.23;
T_9.22 ;
    %vpi_call/w 4 90 "$display", "\342\234\227 64-bit instruction collection broken" {0 0 0};
T_9.23 ;
    %vpi_call/w 4 93 "$display", "\012=== Test Complete ===" {0 0 0};
    %end;
    .thread T_9;
    .scope S_0x55ac49c3e210;
T_10 ;
    %fork t_23, S_0x55ac49cae530;
    %jmp t_22;
    .scope S_0x55ac49cae530;
t_23 ;
    %vpi_call/w 3 475 "$display", "=== Tile Instruction Decoder Example ===" {0 0 0};
    %pushi/vec4 16507, 0, 32;
    %store/vec4 v0x55ac49cae8f0_0, 0, 32;
    %vpi_call/w 3 479 "$display", "\012Testing 32-bit instruction: 0x%08x", v0x55ac49cae8f0_0 {0 0 0};
    %alloc S_0x55ac49cadbc0;
    %load/vec4 v0x55ac49cae8f0_0;
    %store/vec4 v0x55ac49cadf30_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.is_tile_instruction, S_0x55ac49cadbc0;
    %free S_0x55ac49cadbc0;
    %flag_set/vec4 8;
    %jmp/0xz  T_10.0, 8;
    %alloc S_0x55ac49cad740;
    %load/vec4 v0x55ac49cae8f0_0;
    %store/vec4 v0x55ac49cada20_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x55ac49cad740;
    %free S_0x55ac49cad740;
    %store/vec4 v0x55ac49cae730_0, 0, 135;
    %load/vec4 v0x55ac49cae730_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_10.2, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_10.3, 8;
T_10.2 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_10.3, 8;
 ; End of false expr.
    %blend;
T_10.3;
    %vpi_call/w 3 483 "$display", "  Is tile: YES, Length: %0d, Complete: %s", &PV<v0x55ac49cae730_0, 2, 2>, S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x55ac49cae730_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_10.4, 8;
    %alloc S_0x55ac49ca87a0;
    %load/vec4 v0x55ac49cae730_0;
    %parti/u 128, 7, 32;
    %load/vec4 v0x55ac49cae730_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x55ac49ca8be0_0, 0, 2;
    %store/vec4 v0x55ac49ca8b20_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x55ac49ca87a0;
    %free S_0x55ac49ca87a0;
    %store/str v0x55ac49cae830_0;
    %vpi_call/w 3 490 "$display", "  Disassembly: %s", v0x55ac49cae830_0 {0 0 0};
T_10.4 ;
T_10.0 ;
    %vpi_call/w 3 495 "$display", "\012Testing 64-bit instruction:" {0 0 0};
    %pushi/vec4 24699, 0, 32;
    %store/vec4 v0x55ac49cae8f0_0, 0, 32;
    %vpi_call/w 3 497 "$display", "  Word 1: 0x%08x", v0x55ac49cae8f0_0 {0 0 0};
    %alloc S_0x55ac49cad740;
    %load/vec4 v0x55ac49cae8f0_0;
    %store/vec4 v0x55ac49cada20_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x55ac49cad740;
    %free S_0x55ac49cad740;
    %store/vec4 v0x55ac49cae730_0, 0, 135;
    %load/vec4 v0x55ac49cae730_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_10.6, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_10.7, 8;
T_10.6 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_10.7, 8;
 ; End of false expr.
    %blend;
T_10.7;
    %vpi_call/w 3 500 "$display", "  Expected length: %0d, Complete: %s", &PV<v0x55ac49cae730_0, 2, 2>, S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x55ac49cae730_0;
    %parti/u 1, 1, 32;
    %nor/r;
    %flag_set/vec4 8;
    %jmp/0xz  T_10.8, 8;
    %pushi/vec4 2147483771, 0, 32;
    %store/vec4 v0x55ac49cae8f0_0, 0, 32;
    %vpi_call/w 3 506 "$display", "  Word 2: 0x%08x", v0x55ac49cae8f0_0 {0 0 0};
    %alloc S_0x55ac49c3e3a0;
    %load/vec4 v0x55ac49cae730_0;
    %load/vec4 v0x55ac49cae8f0_0;
    %store/vec4 v0x55ac49ca86c0_0, 0, 32;
    %store/vec4 v0x55ac49ca8520_0, 0, 135;
    %callf/vec4 TD_tile_decoder_pkg.add_word_to_collector, S_0x55ac49c3e3a0;
    %free S_0x55ac49c3e3a0;
    %store/vec4 v0x55ac49cae730_0, 0, 135;
    %load/vec4 v0x55ac49cae730_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_10.10, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_10.11, 8;
T_10.10 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_10.11, 8;
 ; End of false expr.
    %blend;
T_10.11;
    %vpi_call/w 3 508 "$display", "  Complete: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x55ac49cae730_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_10.12, 8;
    %alloc S_0x55ac49ca87a0;
    %load/vec4 v0x55ac49cae730_0;
    %parti/u 128, 7, 32;
    %load/vec4 v0x55ac49cae730_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x55ac49ca8be0_0, 0, 2;
    %store/vec4 v0x55ac49ca8b20_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x55ac49ca87a0;
    %free S_0x55ac49ca87a0;
    %store/str v0x55ac49cae830_0;
    %vpi_call/w 3 513 "$display", "  Disassembly: %s", v0x55ac49cae830_0 {0 0 0};
T_10.12 ;
T_10.8 ;
    %vpi_call/w 3 517 "$display", "\012=== Example Complete ===" {0 0 0};
    %end;
    .scope S_0x55ac49c3e210;
t_22 %join;
    %end;
    .thread T_10;
# The file index is used to find the file name in the following table.
:file_names 5;
    "N/A";
    "<interactive>";
    "-";
    "./tile_instruction_decoder.sv";
    "test_tacp_fix.sv";
