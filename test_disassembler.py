#!/usr/bin/env python3
"""
Test suite for the Tile Extension ISA Disassembler
"""

import sys
import os
from tile_disassembler import TileDisassembler


def test_basic_functionality():
    """Test basic disassembler functionality"""
    print("Testing basic functionality...")
    
    disasm = TileDisassembler()
    
    # Test that instructions were loaded
    instructions = disasm.list_instructions()
    print(f"Loaded {len(instructions)} instructions")
    
    if len(instructions) == 0:
        print("ERROR: No instructions loaded!")
        return False
    
    # Print first few instructions
    print("First 10 instructions:")
    for i, instr in enumerate(instructions[:10]):
        print(f"  {i+1}. {instr}")
    
    return True


def test_known_patterns():
    """Test with some known instruction patterns"""
    print("\nTesting known instruction patterns...")
    
    disasm = TileDisassembler()
    
    # Test cases based on the encoding patterns we saw in isa_out.log
    test_cases = [
        # 32-bit instructions
        ("0x7b", 32, "Should match some instruction"),
        ("0x1111011", 32, "ACE_op pattern"),
        ("0x0", 32, "All zeros"),
        ("0xffffffff", 32, "All ones"),
        
        # 64-bit instructions  
        ("0x1111011000000000", 64, "64-bit ACE_op pattern"),
        ("0x0000000000000000", 64, "64-bit all zeros"),
        ("0xffffffffffffffff", 64, "64-bit all ones"),
    ]
    
    for hex_str, bit_width, description in test_cases:
        try:
            binary_data = int(hex_str, 16)
            result = disasm.disassemble_instruction(binary_data, bit_width)
            print(f"  {hex_str:>18} ({bit_width}bit) -> {result if result else 'No match'}")
        except Exception as e:
            print(f"  {hex_str:>18} ({bit_width}bit) -> ERROR: {e}")
    
    return True


def test_hex_string_parsing():
    """Test hex string parsing functionality"""
    print("\nTesting hex string parsing...")
    
    disasm = TileDisassembler()
    
    test_strings = [
        "0x1234",
        "1234",
        "0x12345678",
        "12345678",
        "0x1234567890abcdef",
        "1234567890abcdef",
        "invalid_hex",
        "",
        "0x",
    ]
    
    for hex_str in test_strings:
        result = disasm.disassemble_hex_string(hex_str)
        print(f"  '{hex_str}' -> {result if result else 'No match/Error'}")
    
    return True


def test_instruction_matching():
    """Test instruction matching logic"""
    print("\nTesting instruction matching logic...")
    
    disasm = TileDisassembler()
    
    # Get a few instruction definitions to examine
    sample_instructions = list(disasm.instructions.items())[:5]
    
    for name, instr_def in sample_instructions:
        print(f"\nInstruction: {name}")
        print(f"  File: {instr_def.file_path}")
        print(f"  Encoding: {instr_def.encoding}")
        print(f"  Match value: 0x{instr_def.match_value:x}")
        print(f"  Match mask:  0x{instr_def.match_mask:x}")
        print(f"  Total bits: {instr_def.total_bits}")
        print(f"  Fields:")
        for field in instr_def.fields:
            print(f"    {field.name:15} bits={field.bits:2} attr='{field.attr}' pos=[{field.start_bit}:{field.end_bit}]")
        
        # Test if the instruction matches its own pattern
        test_value = instr_def.match_value
        result = disasm.disassemble_instruction(test_value, instr_def.total_bits)
        print(f"  Self-test: 0x{test_value:x} -> {result}")


def test_field_extraction():
    """Test field extraction from binary data"""
    print("\nTesting field extraction...")
    
    disasm = TileDisassembler()
    
    # Create a test binary value with known bit patterns
    test_value = 0x12345678
    
    print(f"Test value: 0x{test_value:08x} = {test_value:032b}")
    
    # Test extracting different field sizes at different positions
    test_extractions = [
        (0, 4),   # bits 0-3
        (4, 4),   # bits 4-7
        (8, 8),   # bits 8-15
        (16, 16), # bits 16-31
        (0, 32),  # all bits
    ]
    
    for start_bit, num_bits in test_extractions:
        mask = (1 << num_bits) - 1
        extracted = (test_value >> start_bit) & mask
        print(f"  Bits [{start_bit}:{start_bit+num_bits-1}] = 0x{extracted:x} ({extracted})")


def run_interactive_test():
    """Run interactive test mode"""
    print("\nInteractive test mode - enter hex values to disassemble (or 'quit' to exit):")
    
    disasm = TileDisassembler()
    
    while True:
        try:
            user_input = input("Enter hex value: ").strip()
            if user_input.lower() in ['quit', 'exit', 'q']:
                break
            
            if not user_input:
                continue
            
            result = disasm.disassemble_hex_string(user_input)
            if result:
                print(f"  -> {result}")
            else:
                print(f"  -> No matching instruction found")
                
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"  -> Error: {e}")


def main():
    """Main test function"""
    print("Tile Extension ISA Disassembler Test Suite")
    print("=" * 50)
    
    # Run all tests
    tests = [
        test_basic_functionality,
        test_known_patterns,
        test_hex_string_parsing,
        test_instruction_matching,
        test_field_extraction,
    ]
    
    for test_func in tests:
        try:
            test_func()
        except Exception as e:
            print(f"ERROR in {test_func.__name__}: {e}")
    
    # Ask if user wants interactive mode
    if len(sys.argv) > 1 and sys.argv[1] == "--interactive":
        run_interactive_test()
    else:
        print("\nAll tests completed. Run with --interactive for interactive mode.")


if __name__ == "__main__":
    main()
