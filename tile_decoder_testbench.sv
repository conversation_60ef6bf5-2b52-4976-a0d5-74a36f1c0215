// Testbench for Tile Extension ISA Instruction Decoder
// Demonstrates usage of the SystemVerilog decoder functions

`timescale 1ns/1ps

module tile_decoder_testbench;

    import tile_decoder_pkg::*;

    // Test data - real instruction encodings from the Python disassembler
    typedef struct {
        logic [31:0] words[4];
        int num_words;
        string expected_result;
        string description;
    } test_case_t;

    test_case_t test_cases[] = '{
        // 32-bit CSR instruction
        '{
            words: '{32'h0000407b, 32'h00000000, 32'h00000000, 32'h00000000},
            num_words: 1,
            expected_result: "tcsrw.i x0, 0x0, x0",
            description: "32-bit CSR write immediate"
        },
        
        // 64-bit memory load instruction
        '{
            words: '{32'h0000607b, 32'h8000007b, 32'h00000000, 32'h00000000},
            num_words: 2,
            expected_result: "tld.trii.linear.u32.global t0, (x0)",
            description: "64-bit tile load linear"
        },
        
        // 96-bit indexed load instruction (3 words)
        '{
            words: '{32'h0000697b, 32'h8000007b, 32'hf260707b, 32'h00000000},
            num_words: 3,
            expected_result: "tld.trvi.asp.index.u32.global t0, (x0)",
            description: "96-bit tile load indexed"
        },
        
        // 128-bit tile copy instruction (4 words)
        '{
            words: '{32'h3800617b, 32'h8000047b, 32'hf260707b, 32'hf260707b},
            num_words: 4,
            expected_result: "tacp.rvv.asp2.dsttm 0, 0, x0, 0, 0, 0, 0",
            description: "128-bit tile copy"
        }
    };

    // Instruction processing pipeline simulation
    task automatic process_instruction_stream();
        instr_collector_t collector;
        logic collector_active = 1'b0;
        string disasm_result;
        logic [127:0] complete_instruction;
        instr_length_e instr_length;
        
        $display("=== Instruction Stream Processing Test ===");
        $display("");
        
        foreach (test_cases[i]) begin
            $display("Test Case %0d: %s", i+1, test_cases[i].description);
            
            collector_active = 1'b0;
            
            // Process each word in the test case
            for (int w = 0; w < test_cases[i].num_words; w++) begin
                logic [31:0] current_word = test_cases[i].words[w];
                
                $display("  Word %0d: 0x%08x", w+1, current_word);
                
                if (!collector_active) begin
                    // Start new instruction
                    collector = tile_decoder_pkg::init_collector(current_word);
                    collector_active = 1'b1;
                    
                    $display("    -> New instruction detected");
                    $display("    -> Is tile instruction: %s", 
                            collector.is_tile_instr ? "YES" : "NO");
                    $display("    -> Expected length: %s", 
                            collector.expected_length.name());
                    $display("    -> Complete: %s", 
                            collector.is_complete ? "YES" : "NO");
                end else begin
                    // Add word to existing instruction
                    collector = tile_decoder_pkg::add_word_to_collector(
                        collector, current_word);
                    
                    $display("    -> Added to instruction (words: %0d)", 
                            collector.collected_words);
                    $display("    -> Complete: %s", 
                            collector.is_complete ? "YES" : "NO");
                end
                
                // Check if instruction is complete
                if (collector.is_complete) begin
                    complete_instruction = collector.instruction_data;
                    instr_length = collector.expected_length;
                    
                    // Disassemble the complete instruction
                    disasm_result = tile_decoder_pkg::disassemble_instruction(
                        complete_instruction, instr_length);
                    
                    $display("    -> COMPLETE INSTRUCTION:");
                    $display("       Data: 0x%032x", complete_instruction);
                    $display("       Bits: %0d", 
                            tile_instruction_decoder.get_instruction_bits(instr_length));
                    $display("       Disassembly: %s", disasm_result);
                    $display("       Expected:    %s", test_cases[i].expected_result);
                    
                    // Check result
                    if (disasm_result == test_cases[i].expected_result) begin
                        $display("       Status: PASS ✓");
                    end else begin
                        $display("       Status: FAIL ✗");
                    end
                    
                    collector_active = 1'b0;
                    break;
                end
            end
            
            $display("");
        end
    endtask

    // Test individual functions
    task automatic test_individual_functions();
        logic [31:0] test_word;
        instr_length_e length;
        logic is_tile;
        logic [6:0] ace_op;
        
        $display("=== Individual Function Tests ===");
        $display("");
        
        // Test ACE_OP extraction
        test_word = 32'h0000607b;
        ace_op = tile_decoder_pkg::extract_ace_op(test_word);
        $display("ACE_OP extraction test:");
        $display("  Input: 0x%08x", test_word);
        $display("  ACE_OP: 0x%02x (binary: %07b)", ace_op, ace_op);
        $display("  Expected: 0x7b (binary: 1111011)");
        $display("  Result: %s", (ace_op == 7'b1111011) ? "PASS ✓" : "FAIL ✗");
        $display("");
        
        // Test instruction length detection
        $display("Instruction length detection tests:");
        
        logic [31:0] length_test_words[] = '{
            32'h0000407b,  // 32-bit CSR
            32'h0000607b,  // 64-bit memory
            32'h0000697b,  // 96-bit indexed
            32'h3800617b   // 128-bit (part of tacp)
        };
        
        string expected_lengths[] = '{"INSTR_32BIT", "INSTR_64BIT", "INSTR_96BIT", "INSTR_64BIT"};
        
        foreach (length_test_words[i]) begin
            length = tile_decoder_pkg::get_instruction_length(length_test_words[i]);
            $display("  Word: 0x%08x -> %s (expected: %s)",
                    length_test_words[i], length.name(), expected_lengths[i]);
        end
        $display("");
        
        // Test tile instruction detection
        $display("Tile instruction detection tests:");
        
        logic [31:0] tile_test_words[] = '{
            32'h0000607b,  // Tile instruction
            32'h12345678,  // Non-tile instruction
            32'h0000407b   // Tile CSR instruction
        };
        
        logic expected_tile[] = '{1'b1, 1'b0, 1'b1};
        
        foreach (tile_test_words[i]) begin
            is_tile = tile_decoder_pkg::is_tile_instruction(tile_test_words[i]);
            $display("  Word: 0x%08x -> %s (expected: %s)",
                    tile_test_words[i],
                    is_tile ? "TILE" : "NON-TILE",
                    expected_tile[i] ? "TILE" : "NON-TILE");
        end
        $display("");
    endtask

    // Test edge cases
    task automatic test_edge_cases();
        instr_collector_t collector;
        logic [31:0] invalid_word = 32'h12345678; // Non-tile instruction
        
        $display("=== Edge Case Tests ===");
        $display("");
        
        // Test non-tile instruction
        $display("Non-tile instruction test:");
        collector = tile_decoder_pkg::init_collector(invalid_word);
        $display("  Input: 0x%08x", invalid_word);
        $display("  Is tile: %s", collector.is_tile_instr ? "YES" : "NO");
        $display("  Length: %s", collector.expected_length.name());
        $display("  Complete: %s", collector.is_complete ? "YES" : "NO");
        $display("");
        
        // Test overflow protection
        $display("Overflow protection test:");
        collector = tile_decoder_pkg::init_collector(32'h0000607b); // 64-bit instruction

        // Try to add too many words
        for (int i = 0; i < 6; i++) begin
            collector = tile_decoder_pkg::add_word_to_collector(
                collector, 32'h12345678);
            $display("  After adding word %0d: collected=%0d, complete=%s", 
                    i+1, collector.collected_words, 
                    collector.is_complete ? "YES" : "NO");
        end
        $display("");
    endtask

    // Main test sequence
    initial begin
        $display("Tile Extension ISA Decoder Testbench");
        $display("=====================================");
        $display("");
        
        test_individual_functions();
        process_instruction_stream();
        test_edge_cases();
        
        $display("=== Test Summary ===");
        $display("All tests completed. Check results above.");
        $display("");
        
        $finish;
    end

endmodule
