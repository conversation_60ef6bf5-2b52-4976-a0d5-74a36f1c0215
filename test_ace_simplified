#! /usr/bin/vvp
:ivl_version "11.0 (stable)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/system.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_sys.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_textio.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2005_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/va_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2009.vpi";
S_0x5633828eafb0 .scope package, "$unit" "$unit" 2 1;
 .timescale 0 0;
S_0x5633828bdc00 .scope package, "tile_decoder_pkg" "tile_decoder_pkg" 3 5;
 .timescale 0 0;
P_0x5633828bdd90 .param/l "TILE_ACE_OP" 0 3 16, C4<1111011>;
enum0x563382850f80 .enum4 (2)
   "INSTR_32BIT" 2'b00,
   "INSTR_64BIT" 2'b01,
   "INSTR_96BIT" 2'b10,
   "INSTR_128BIT" 2'b11
 ;
S_0x5633828c33a0 .scope autofunction.vec4.s134, "add_word_to_collector" "add_word_to_collector" 3 137, 3 137 0, S_0x5633828bdc00;
 .timescale 0 0;
; Variable add_word_to_collector is vec4 return value of scope S_0x5633828c33a0
v0x563382939690_0 .var "collector", 133 0;
v0x563382939770_0 .var "new_collector", 133 0;
v0x563382939830_0 .var "word", 31 0;
TD_tile_decoder_pkg.add_word_to_collector ;
    %load/vec4 v0x563382939690_0;
    %store/vec4 v0x563382939770_0, 0, 134;
    %load/vec4 v0x563382939690_0;
    %parti/u 1, 1, 32;
    %nor/r;
    %load/vec4 v0x563382939690_0;
    %parti/u 2, 4, 32;
    %pad/u 32;
    %cmpi/u 4, 0, 32;
    %flag_get/vec4 5;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.0, 8;
    %load/vec4 v0x563382939690_0;
    %parti/u 2, 4, 32;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_0.2, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_0.3, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_0.4, 6;
    %jmp T_0.5;
T_0.2 ;
    %load/vec4 v0x563382939830_0;
    %ix/load 4, 38, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x563382939770_0, 4, 32;
    %jmp T_0.5;
T_0.3 ;
    %load/vec4 v0x563382939830_0;
    %ix/load 4, 70, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x563382939770_0, 4, 32;
    %jmp T_0.5;
T_0.4 ;
    %load/vec4 v0x563382939830_0;
    %ix/load 4, 102, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x563382939770_0, 4, 32;
    %jmp T_0.5;
T_0.5 ;
    %pop/vec4 1;
    %load/vec4 v0x563382939690_0;
    %parti/u 2, 4, 32;
    %addi 1, 0, 2;
    %ix/load 4, 4, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x563382939770_0, 4, 2;
    %load/vec4 v0x563382939690_0;
    %parti/u 2, 2, 32;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_0.6, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_0.7, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_0.8, 6;
    %pushi/vec4 1, 0, 1;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x563382939770_0, 4, 1;
    %jmp T_0.10;
T_0.6 ;
    %pushi/vec4 2, 0, 32;
    %load/vec4 v0x563382939770_0;
    %parti/u 2, 4, 32;
    %pad/u 32;
    %cmp/u;
    %flag_get/vec4 4;
    %flag_get/vec4 5;
    %or;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x563382939770_0, 4, 1;
    %jmp T_0.10;
T_0.7 ;
    %pushi/vec4 3, 0, 32;
    %load/vec4 v0x563382939770_0;
    %parti/u 2, 4, 32;
    %pad/u 32;
    %cmp/u;
    %flag_get/vec4 4;
    %flag_get/vec4 5;
    %or;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x563382939770_0, 4, 1;
    %jmp T_0.10;
T_0.8 ;
    %pushi/vec4 4, 0, 32;
    %load/vec4 v0x563382939770_0;
    %parti/u 2, 4, 32;
    %pad/u 32;
    %cmp/u;
    %flag_get/vec4 4;
    %flag_get/vec4 5;
    %or;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x563382939770_0, 4, 1;
    %jmp T_0.10;
T_0.10 ;
    %pop/vec4 1;
T_0.0 ;
    %load/vec4 v0x563382939770_0;
    %ret/vec4 0, 0, 134;  Assign to add_word_to_collector (store_vec4_to_lval)
    %disable S_0x5633828c33a0;
    %end;
S_0x563382939910 .scope autofunction.str, "disassemble_instruction" "disassemble_instruction" 3 445, 3 445 0, S_0x5633828bdc00;
 .timescale 0 0;
; Variable disassemble_instruction is string return value of scope S_0x563382939910
v0x563382939bd0_0 .var/str "instr_name";
v0x563382939c90_0 .var "instruction_data", 127 0;
v0x563382939d50_0 .var "length", 1 0;
v0x563382939e30_0 .var/str "operands";
v0x563382939f40_0 .var/str "result";
TD_tile_decoder_pkg.disassemble_instruction ;
    %alloc S_0x56338293a3a0;
    %load/vec4 v0x563382939c90_0;
    %load/vec4 v0x563382939d50_0;
    %store/vec4 v0x56338293b5c0_0, 0, 2;
    %store/vec4 v0x56338293b500_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.extract_instruction_name, S_0x56338293a3a0;
    %free S_0x56338293a3a0;
    %store/str v0x563382939bd0_0;
    %alloc S_0x56338293bbf0;
    %load/vec4 v0x563382939c90_0;
    %load/vec4 v0x563382939d50_0;
    %load/str v0x563382939bd0_0;
    %store/str v0x56338293d770_0;
    %store/vec4 v0x56338293d8f0_0, 0, 2;
    %store/vec4 v0x56338293d830_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.format_operands, S_0x56338293bbf0;
    %free S_0x56338293bbf0;
    %store/str v0x563382939e30_0;
    %load/str v0x563382939e30_0;
    %pushi/str "";
    %cmp/str;
    %flag_inv 4;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_1.11, 8;
    %vpi_call/w 3 455 "$sformat", v0x563382939f40_0, "%s %s", v0x563382939bd0_0, v0x563382939e30_0 {0 0 0};
    %jmp T_1.12;
T_1.11 ;
    %load/str v0x563382939bd0_0;
    %store/str v0x563382939f40_0;
T_1.12 ;
    %load/str v0x563382939f40_0;
    %ret/str 0; Assign to disassemble_instruction
    %disable S_0x563382939910;
    %end;
S_0x56338293a000 .scope autofunction.vec4.s7, "extract_ace_op" "extract_ace_op" 3 28, 3 28 0, S_0x5633828bdc00;
 .timescale 0 0;
; Variable extract_ace_op is vec4 return value of scope S_0x56338293a000
v0x56338293a2c0_0 .var "word", 31 0;
TD_tile_decoder_pkg.extract_ace_op ;
    %load/vec4 v0x56338293a2c0_0;
    %parti/s 7, 0, 2;
    %ret/vec4 0, 0, 7;  Assign to extract_ace_op (store_vec4_to_lval)
    %disable S_0x56338293a000;
    %end;
S_0x56338293a3a0 .scope autofunction.str, "extract_instruction_name" "extract_instruction_name" 3 178, 3 178 0, S_0x5633828bdc00;
 .timescale 0 0;
v0x56338293b340_0 .var "ace_op", 6 0;
; Variable extract_instruction_name is string return value of scope S_0x56338293a3a0
v0x56338293b500_0 .var "instruction_data", 127 0;
v0x56338293b5c0_0 .var "length", 1 0;
v0x56338293b6a0_0 .var "lsuop", 1 0;
v0x56338293b7d0_0 .var "memuop", 5 0;
v0x56338293b8b0_0 .var "miscop", 2 0;
v0x56338293b990_0 .var "offseten", 0 0;
v0x56338293ba50_0 .var "rmten", 0 0;
v0x56338293bb10_0 .var "tuop", 2 0;
TD_tile_decoder_pkg.extract_instruction_name ;
    %load/vec4 v0x56338293b500_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0x56338293b340_0, 0, 7;
    %load/vec4 v0x56338293b340_0;
    %cmpi/ne 123, 0, 7;
    %jmp/0xz  T_3.13, 4;
    %pushi/str "unknown_non_tile";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
T_3.13 ;
    %load/vec4 v0x56338293b500_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0x56338293bb10_0, 0, 3;
    %load/vec4 v0x56338293bb10_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_3.15, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 3;
    %cmp/u;
    %jmp/1 T_3.16, 6;
    %dup/vec4;
    %pushi/vec4 6, 0, 3;
    %cmp/u;
    %jmp/1 T_3.17, 6;
    %dup/vec4;
    %pushi/vec4 4, 0, 3;
    %cmp/u;
    %jmp/1 T_3.18, 6;
    %dup/vec4;
    %pushi/vec4 5, 0, 3;
    %cmp/u;
    %jmp/1 T_3.19, 6;
    %dup/vec4;
    %pushi/vec4 7, 0, 3;
    %cmp/u;
    %jmp/1 T_3.20, 6;
    %pushi/str "unknown_tile";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.22;
T_3.15 ;
    %load/vec4 v0x56338293b500_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x56338293b7d0_0, 0, 6;
    %load/vec4 v0x56338293b500_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0x56338293b6a0_0, 0, 2;
    %load/vec4 v0x56338293b7d0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 6;
    %cmp/u;
    %jmp/1 T_3.23, 6;
    %dup/vec4;
    %pushi/vec4 8, 0, 6;
    %cmp/u;
    %jmp/1 T_3.24, 6;
    %dup/vec4;
    %pushi/vec4 28, 0, 6;
    %cmp/u;
    %jmp/1 T_3.25, 6;
    %jmp T_3.26;
T_3.23 ;
    %load/vec4 v0x56338293b6a0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_3.27, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_3.28, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_3.29, 6;
    %jmp T_3.30;
T_3.27 ;
    %load/vec4 v0x56338293b5c0_0;
    %cmpi/e 1, 0, 2;
    %jmp/0xz  T_3.31, 4;
    %load/vec4 v0x56338293b500_0;
    %parti/s 1, 47, 7;
    %store/vec4 v0x56338293b990_0, 0, 1;
    %load/vec4 v0x56338293b500_0;
    %parti/s 1, 52, 7;
    %store/vec4 v0x56338293ba50_0, 0, 1;
    %load/vec4 v0x56338293b990_0;
    %nor/r;
    %load/vec4 v0x56338293ba50_0;
    %nor/r;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.33, 8;
    %pushi/str "tld.trii.linear.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.34;
T_3.33 ;
    %load/vec4 v0x56338293b990_0;
    %nor/r;
    %load/vec4 v0x56338293ba50_0;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.35, 8;
    %pushi/str "tld.trii.linear.u32.global.remote";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.36;
T_3.35 ;
    %load/vec4 v0x56338293b990_0;
    %load/vec4 v0x56338293ba50_0;
    %nor/r;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.37, 8;
    %pushi/str "tld.trir.linear.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.38;
T_3.37 ;
    %pushi/str "tld.trir.linear.u32.global.remote";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
T_3.38 ;
T_3.36 ;
T_3.34 ;
T_3.31 ;
    %jmp T_3.30;
T_3.28 ;
    %pushi/str "tld.trri.stride.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.30;
T_3.29 ;
    %load/vec4 v0x56338293b5c0_0;
    %cmpi/e 2, 0, 2;
    %jmp/0xz  T_3.39, 4;
    %load/vec4 v0x56338293b500_0;
    %parti/s 1, 79, 8;
    %store/vec4 v0x56338293b990_0, 0, 1;
    %load/vec4 v0x56338293b990_0;
    %nor/r;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.41, 8;
    %pushi/str "tld.trvi.asp.index.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.42;
T_3.41 ;
    %pushi/str "tld.trvr.asp.index.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
T_3.42 ;
T_3.39 ;
    %jmp T_3.30;
T_3.30 ;
    %pop/vec4 1;
    %jmp T_3.26;
T_3.24 ;
    %pushi/str "tst.trvi.index.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.26;
T_3.25 ;
    %load/vec4 v0x56338293b5c0_0;
    %cmpi/e 3, 0, 2;
    %jmp/0xz  T_3.43, 4;
    %fork t_1, S_0x56338293a580;
    %jmp t_0;
    .scope S_0x56338293a580;
t_1 ;
    %load/vec4 v0x56338293b500_0;
    %parti/s 1, 73, 8;
    %store/vec4 v0x56338293a860_0, 0, 1;
    %load/vec4 v0x56338293b500_0;
    %parti/s 1, 74, 8;
    %store/vec4 v0x56338293a780_0, 0, 1;
    %load/vec4 v0x56338293a860_0;
    %nor/r;
    %load/vec4 v0x56338293a780_0;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.45, 8;
    %pushi/str "tacp.rvv.asp2.dsttm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.46;
T_3.45 ;
    %load/vec4 v0x56338293a860_0;
    %load/vec4 v0x56338293a780_0;
    %nor/r;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.47, 8;
    %pushi/str "tacp.vvr.asp2.srctm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.48;
T_3.47 ;
    %pushi/str "tacp.rvrv.asp2.mbar.dsttm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
T_3.48 ;
T_3.46 ;
    %end;
    .scope S_0x56338293a3a0;
t_0 %join;
T_3.43 ;
    %jmp T_3.26;
T_3.26 ;
    %pop/vec4 1;
    %jmp T_3.22;
T_3.16 ;
    %pushi/str "tmma.ttt";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.22;
T_3.17 ;
    %load/vec4 v0x56338293b5c0_0;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_3.49, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_3.50, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_3.51, 6;
    %pushi/str "unknown_tuop110";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.53;
T_3.49 ;
    %pushi/str "tld_64bit";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.53;
T_3.50 ;
    %pushi/str "tld_96bit";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.53;
T_3.51 ;
    %pushi/str "tacp_128bit";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.53;
T_3.53 ;
    %pop/vec4 1;
    %jmp T_3.22;
T_3.18 ;
    %fork t_3, S_0x56338293a920;
    %jmp t_2;
    .scope S_0x56338293a920;
t_3 ;
    %load/vec4 v0x56338293b500_0;
    %parti/s 2, 30, 6;
    %store/vec4 v0x56338293ab20_0, 0, 2;
    %load/vec4 v0x56338293ab20_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_3.54, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_3.55, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_3.56, 6;
    %pushi/str "unknown_csr";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.58;
T_3.54 ;
    %pushi/str "tcsrw.i";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.58;
T_3.55 ;
    %pushi/str "tcsrr.r";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.58;
T_3.56 ;
    %pushi/str "tcsrw.r";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.58;
T_3.58 ;
    %pop/vec4 1;
    %end;
    .scope S_0x56338293a3a0;
t_2 %join;
    %jmp T_3.22;
T_3.19 ;
    %fork t_5, S_0x56338293ac00;
    %jmp t_4;
    .scope S_0x56338293ac00;
t_5 ;
    %load/vec4 v0x56338293b500_0;
    %parti/s 3, 23, 6;
    %store/vec4 v0x56338293ade0_0, 0, 3;
    %load/vec4 v0x56338293b500_0;
    %parti/s 3, 28, 6;
    %store/vec4 v0x56338293aec0_0, 0, 3;
    %load/vec4 v0x56338293ade0_0;
    %pushi/vec4 1, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x56338293aec0_0;
    %pushi/vec4 7, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.59, 8;
    %load/vec4 v0x56338293b500_0;
    %parti/s 1, 26, 6;
    %cmpi/e 1, 0, 1;
    %jmp/0xz  T_3.61, 4;
    %pushi/str "twait.mem";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.62;
T_3.61 ;
    %pushi/str "twait";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
T_3.62 ;
    %jmp T_3.60;
T_3.59 ;
    %pushi/str "unknown_sync";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
T_3.60 ;
    %end;
    .scope S_0x56338293a3a0;
t_4 %join;
    %jmp T_3.22;
T_3.20 ;
    %fork t_7, S_0x56338293afa0;
    %jmp t_6;
    .scope S_0x56338293afa0;
t_7 ;
    %load/vec4 v0x56338293b500_0;
    %parti/s 3, 26, 6;
    %store/vec4 v0x56338293b260_0, 0, 3;
    %load/vec4 v0x56338293b500_0;
    %parti/s 1, 31, 6;
    %store/vec4 v0x56338293b180_0, 0, 1;
    %load/vec4 v0x56338293b180_0;
    %cmpi/e 1, 0, 1;
    %jmp/0xz  T_3.63, 4;
    %load/vec4 v0x56338293b260_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_3.65, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 3;
    %cmp/u;
    %jmp/1 T_3.66, 6;
    %pushi/str "unknown_ace_misc";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.68;
T_3.65 ;
    %pushi/str "ace_bsync";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.68;
T_3.66 ;
    %pushi/str "ace_nbsync";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %jmp T_3.68;
T_3.68 ;
    %pop/vec4 1;
    %jmp T_3.64;
T_3.63 ;
    %pushi/str "unknown_ace";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
T_3.64 ;
    %end;
    .scope S_0x56338293a3a0;
t_6 %join;
    %jmp T_3.22;
T_3.22 ;
    %pop/vec4 1;
    %pushi/str "unknown";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x56338293a3a0;
    %end;
S_0x56338293a580 .scope autobegin, "$unm_blk_19" "$unm_blk_19" 3 234, 3 234 0, S_0x56338293a3a0;
 .timescale 0 0;
v0x56338293a780_0 .var "dsttm", 0 0;
v0x56338293a860_0 .var "srctm", 0 0;
S_0x56338293a920 .scope autobegin, "$unm_blk_21" "$unm_blk_21" 3 264, 3 264 0, S_0x56338293a3a0;
 .timescale 0 0;
v0x56338293ab20_0 .var "rw", 1 0;
S_0x56338293ac00 .scope autobegin, "$unm_blk_22" "$unm_blk_22" 3 274, 3 274 0, S_0x56338293a3a0;
 .timescale 0 0;
v0x56338293ade0_0 .var "ctrluop", 2 0;
v0x56338293aec0_0 .var "waitop", 2 0;
S_0x56338293afa0 .scope autobegin, "$unm_blk_25" "$unm_blk_25" 3 292, 3 292 0, S_0x56338293a3a0;
 .timescale 0 0;
v0x56338293b180_0 .var "ace_misc_en", 0 0;
v0x56338293b260_0 .var "miscop", 2 0;
S_0x56338293bbf0 .scope autofunction.str, "format_operands" "format_operands" 3 314, 3 314 0, S_0x5633828bdc00;
 .timescale 0 0;
; Variable format_operands is string return value of scope S_0x56338293bbf0
v0x56338293d770_0 .var/str "instr_name";
v0x56338293d830_0 .var "instruction_data", 127 0;
v0x56338293d8f0_0 .var "length", 1 0;
v0x56338293d9d0_0 .var/str "operands";
v0x56338293dae0_0 .var "rs1", 4 0;
v0x56338293dbc0_0 .var "rs2", 4 0;
v0x56338293dca0_0 .var "td", 7 0;
TD_tile_decoder_pkg.format_operands ;
    %pushi/str "";
    %store/str v0x56338293d9d0_0;
    %load/vec4 v0x56338293d8f0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_4.69, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_4.70, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_4.71, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_4.72, 6;
    %jmp T_4.73;
T_4.69 ;
    %load/str v0x56338293d770_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 4, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tcsr";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.74, 8;
T_4.74 ;
    %jmp T_4.73;
T_4.70 ;
    %fork t_9, S_0x56338293bdd0;
    %jmp t_8;
    .scope S_0x56338293bdd0;
t_9 ;
    %load/vec4 v0x56338293d830_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0x56338293c0d0_0, 0, 3;
    %load/vec4 v0x56338293d830_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x56338293bfd0_0, 0, 6;
    %load/vec4 v0x56338293d830_0;
    %parti/s 3, 44, 7;
    %store/vec4 v0x56338293c1b0_0, 0, 3;
    %load/vec4 v0x56338293c0d0_0;
    %pushi/vec4 6, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x56338293bfd0_0;
    %pushi/vec4 1, 0, 6;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %load/vec4 v0x56338293c1b0_0;
    %pushi/vec4 1, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.76, 8;
    %load/vec4 v0x56338293d830_0;
    %parti/s 8, 55, 7;
    %store/vec4 v0x56338293dca0_0, 0, 8;
    %load/vec4 v0x56338293d830_0;
    %parti/s 5, 47, 7;
    %store/vec4 v0x56338293dae0_0, 0, 5;
    %load/vec4 v0x56338293d830_0;
    %parti/s 5, 20, 6;
    %store/vec4 v0x56338293dbc0_0, 0, 5;
    %jmp T_4.77;
T_4.76 ;
    %load/str v0x56338293d770_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 3, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tld";
    %cmp/str;
    %flag_get/vec4 4;
    %load/str v0x56338293d770_0;
    %ix/load 4, 4, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 3, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "trr";
    %cmp/str;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.78, 8;
    %load/vec4 v0x56338293d830_0;
    %parti/s 8, 55, 7;
    %store/vec4 v0x56338293dca0_0, 0, 8;
    %load/vec4 v0x56338293d830_0;
    %parti/s 5, 47, 7;
    %store/vec4 v0x56338293dae0_0, 0, 5;
    %load/vec4 v0x56338293d830_0;
    %parti/s 5, 20, 6;
    %store/vec4 v0x56338293dbc0_0, 0, 5;
    %jmp T_4.79;
T_4.78 ;
    %load/vec4 v0x56338293d830_0;
    %parti/s 8, 32, 7;
    %store/vec4 v0x56338293dca0_0, 0, 8;
    %load/vec4 v0x56338293d830_0;
    %parti/s 5, 48, 7;
    %store/vec4 v0x56338293dae0_0, 0, 5;
T_4.79 ;
T_4.77 ;
    %end;
    .scope S_0x56338293bbf0;
t_8 %join;
    %jmp T_4.73;
T_4.71 ;
    %load/vec4 v0x56338293d830_0;
    %parti/s 8, 48, 7;
    %store/vec4 v0x56338293dca0_0, 0, 8;
    %load/vec4 v0x56338293d830_0;
    %parti/s 5, 43, 7;
    %store/vec4 v0x56338293dae0_0, 0, 5;
    %jmp T_4.73;
T_4.72 ;
    %load/vec4 v0x56338293d830_0;
    %parti/s 5, 47, 7;
    %store/vec4 v0x56338293dae0_0, 0, 5;
    %jmp T_4.73;
T_4.73 ;
    %pop/vec4 1;
    %load/vec4 v0x56338293d8f0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_4.80, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_4.81, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_4.82, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_4.83, 6;
    %jmp T_4.84;
T_4.80 ;
    %load/str v0x56338293d770_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 4, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tcsr";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.85, 8;
    %fork t_11, S_0x56338293c270;
    %jmp t_10;
    .scope S_0x56338293c270;
t_11 ;
    %load/vec4 v0x56338293d830_0;
    %parti/s 2, 30, 6;
    %store/vec4 v0x56338293c6f0_0, 0, 2;
    %load/vec4 v0x56338293d830_0;
    %parti/s 9, 20, 6;
    %store/vec4 v0x56338293c470_0, 0, 9;
    %load/vec4 v0x56338293d830_0;
    %parti/s 5, 7, 4;
    %store/vec4 v0x56338293c550_0, 0, 5;
    %load/vec4 v0x56338293d830_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x56338293c630_0, 0, 5;
    %load/vec4 v0x56338293c6f0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_4.87, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_4.88, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_4.89, 6;
    %pushi/str "unknown";
    %store/str v0x56338293d9d0_0;
    %jmp T_4.91;
T_4.87 ;
    %vpi_call/w 3 383 "$sformat", v0x56338293d9d0_0, "0x%0x", v0x56338293c630_0 {0 0 0};
    %jmp T_4.91;
T_4.88 ;
    %vpi_call/w 3 386 "$sformat", v0x56338293d9d0_0, "x%0d", v0x56338293c550_0 {0 0 0};
    %jmp T_4.91;
T_4.89 ;
    %vpi_call/w 3 389 "$sformat", v0x56338293d9d0_0, "x%0d", v0x56338293c630_0 {0 0 0};
    %jmp T_4.91;
T_4.91 ;
    %pop/vec4 1;
    %end;
    .scope S_0x56338293bbf0;
t_10 %join;
    %jmp T_4.86;
T_4.85 ;
    %load/str v0x56338293d770_0;
    %pushi/str "twait";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %load/str v0x56338293d770_0;
    %pushi/str "twait.mem";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %jmp/0xz  T_4.92, 9;
    %pushi/str "";
    %store/str v0x56338293d9d0_0;
    %jmp T_4.93;
T_4.92 ;
    %load/str v0x56338293d770_0;
    %pushi/str "twait.i.load.global";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %load/str v0x56338293d770_0;
    %pushi/str "twait.i.load.share";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %load/str v0x56338293d770_0;
    %pushi/str "twait.i.store.global";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %flag_or 8, 9;
    %load/str v0x56338293d770_0;
    %pushi/str "twait.i.store.share";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %jmp/0xz  T_4.94, 9;
    %fork t_13, S_0x56338293c7d0;
    %jmp t_12;
    .scope S_0x56338293c7d0;
t_13 ;
    %load/vec4 v0x56338293d830_0;
    %parti/s 8, 15, 5;
    %store/vec4 v0x56338293c9e0_0, 0, 8;
    %vpi_call/w 3 400 "$sformat", v0x56338293d9d0_0, "%0d", v0x56338293c9e0_0 {0 0 0};
    %end;
    .scope S_0x56338293bbf0;
t_12 %join;
    %jmp T_4.95;
T_4.94 ;
    %load/str v0x56338293d770_0;
    %pushi/str "twait.r.load.global";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %load/str v0x56338293d770_0;
    %pushi/str "twait.r.load.share";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %load/str v0x56338293d770_0;
    %pushi/str "twait.r.store.global";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %flag_or 8, 9;
    %load/str v0x56338293d770_0;
    %pushi/str "twait.r.store.share";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %load/str v0x56338293d770_0;
    %pushi/str "twait.r.tacp_cg";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %flag_or 8, 9;
    %load/str v0x56338293d770_0;
    %pushi/str "twait.r.rmtfence";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %jmp/0xz  T_4.96, 9;
    %fork t_15, S_0x56338293cac0;
    %jmp t_14;
    .scope S_0x56338293cac0;
t_15 ;
    %load/vec4 v0x56338293d830_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x56338293cca0_0, 0, 5;
    %vpi_call/w 3 406 "$sformat", v0x56338293d9d0_0, "x%0d", v0x56338293cca0_0 {0 0 0};
    %end;
    .scope S_0x56338293bbf0;
t_14 %join;
    %jmp T_4.97;
T_4.96 ;
    %load/str v0x56338293d770_0;
    %pushi/str "tsync.i";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.98, 8;
    %fork t_17, S_0x56338293cda0;
    %jmp t_16;
    .scope S_0x56338293cda0;
t_17 ;
    %load/vec4 v0x56338293d830_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x56338293cfd0_0, 0, 5;
    %vpi_call/w 3 410 "$sformat", v0x56338293d9d0_0, "%0d", v0x56338293cfd0_0 {0 0 0};
    %end;
    .scope S_0x56338293bbf0;
t_16 %join;
    %jmp T_4.99;
T_4.98 ;
    %load/str v0x56338293d770_0;
    %pushi/str "tkill.r";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.100, 8;
    %fork t_19, S_0x56338293d0d0;
    %jmp t_18;
    .scope S_0x56338293d0d0;
t_19 ;
    %load/vec4 v0x56338293d830_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x56338293d2b0_0, 0, 5;
    %vpi_call/w 3 414 "$sformat", v0x56338293d9d0_0, "x%0d", v0x56338293d2b0_0 {0 0 0};
    %end;
    .scope S_0x56338293bbf0;
t_18 %join;
    %jmp T_4.101;
T_4.100 ;
    %load/str v0x56338293d770_0;
    %pushi/str "ace_bsync";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %load/str v0x56338293d770_0;
    %pushi/str "ace_nbsync";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %jmp/0xz  T_4.102, 9;
    %fork t_21, S_0x56338293d3b0;
    %jmp t_20;
    .scope S_0x56338293d3b0;
t_21 ;
    %load/vec4 v0x56338293d830_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x56338293d590_0, 0, 5;
    %vpi_call/w 3 418 "$sformat", v0x56338293d9d0_0, "x%0d", v0x56338293d590_0 {0 0 0};
    %end;
    .scope S_0x56338293bbf0;
t_20 %join;
    %jmp T_4.103;
T_4.102 ;
    %pushi/str "0";
    %store/str v0x56338293d9d0_0;
T_4.103 ;
T_4.101 ;
T_4.99 ;
T_4.97 ;
T_4.95 ;
T_4.93 ;
T_4.86 ;
    %jmp T_4.84;
T_4.81 ;
    %vpi_call/w 3 427 "$sformat", v0x56338293d9d0_0, "t%0d, (x%0d)", v0x56338293dca0_0, v0x56338293dae0_0 {0 0 0};
    %jmp T_4.84;
T_4.82 ;
    %vpi_call/w 3 432 "$sformat", v0x56338293d9d0_0, "t%0d, (x%0d)", v0x56338293dca0_0, v0x56338293dae0_0 {0 0 0};
    %jmp T_4.84;
T_4.83 ;
    %vpi_call/w 3 437 "$sformat", v0x56338293d9d0_0, "t%0d, t%0d, x%0d", v0x56338293dca0_0, v0x56338293dca0_0, v0x56338293dae0_0 {0 0 0};
    %jmp T_4.84;
T_4.84 ;
    %pop/vec4 1;
    %load/str v0x56338293d9d0_0;
    %ret/str 0; Assign to format_operands
    %disable S_0x56338293bbf0;
    %end;
S_0x56338293bdd0 .scope autobegin, "$unm_blk_29" "$unm_blk_29" 3 333, 3 333 0, S_0x56338293bbf0;
 .timescale 0 0;
v0x56338293bfd0_0 .var "memuop_field", 5 0;
v0x56338293c0d0_0 .var "tuop_first", 2 0;
v0x56338293c1b0_0 .var "tuop_second", 2 0;
S_0x56338293c270 .scope autobegin, "$unm_blk_36" "$unm_blk_36" 3 374, 3 374 0, S_0x56338293bbf0;
 .timescale 0 0;
v0x56338293c470_0 .var "csr_addr", 8 0;
v0x56338293c550_0 .var "rd", 4 0;
v0x56338293c630_0 .var "rs1_or_imm", 4 0;
v0x56338293c6f0_0 .var "rw", 1 0;
S_0x56338293c7d0 .scope autobegin, "$unm_blk_41" "$unm_blk_41" 3 397, 3 397 0, S_0x56338293bbf0;
 .timescale 0 0;
v0x56338293c9e0_0 .var "cnt", 7 0;
S_0x56338293cac0 .scope autobegin, "$unm_blk_42" "$unm_blk_42" 3 403, 3 403 0, S_0x56338293bbf0;
 .timescale 0 0;
v0x56338293cca0_0 .var "rs1", 4 0;
S_0x56338293cda0 .scope autobegin, "$unm_blk_43" "$unm_blk_43" 3 407, 3 407 0, S_0x56338293bbf0;
 .timescale 0 0;
v0x56338293cfd0_0 .var "sync_id", 4 0;
S_0x56338293d0d0 .scope autobegin, "$unm_blk_44" "$unm_blk_44" 3 411, 3 411 0, S_0x56338293bbf0;
 .timescale 0 0;
v0x56338293d2b0_0 .var "rs1", 4 0;
S_0x56338293d3b0 .scope autobegin, "$unm_blk_45" "$unm_blk_45" 3 415, 3 415 0, S_0x56338293bbf0;
 .timescale 0 0;
v0x56338293d590_0 .var "sync_id", 4 0;
S_0x56338293dd80 .scope autofunction.vec2.u32, "get_instruction_bits" "get_instruction_bits" 3 167, 3 167 0, S_0x5633828bdc00;
 .timescale 0 0;
; Variable get_instruction_bits is bool return value of scope S_0x56338293dd80
v0x56338293e060_0 .var "length", 1 0;
TD_tile_decoder_pkg.get_instruction_bits ;
    %load/vec4 v0x56338293e060_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_5.104, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_5.105, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_5.106, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_5.107, 6;
    %pushi/vec4 32, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x56338293dd80;
    %jmp T_5.109;
T_5.104 ;
    %pushi/vec4 32, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x56338293dd80;
    %jmp T_5.109;
T_5.105 ;
    %pushi/vec4 64, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x56338293dd80;
    %jmp T_5.109;
T_5.106 ;
    %pushi/vec4 96, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x56338293dd80;
    %jmp T_5.109;
T_5.107 ;
    %pushi/vec4 128, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x56338293dd80;
    %jmp T_5.109;
T_5.109 ;
    %pop/vec4 1;
    %end;
S_0x56338293e140 .scope autofunction.vec4.s2, "get_instruction_length" "get_instruction_length" 3 40, 3 40 0, S_0x5633828bdc00;
 .timescale 0 0;
v0x56338293e320_0 .var "ace_op", 6 0;
v0x56338293e420_0 .var "first_word", 31 0;
; Variable get_instruction_length is vec4 return value of scope S_0x56338293e140
v0x56338293e5c0_0 .var "lsuop", 1 0;
v0x56338293e6a0_0 .var "memuop", 5 0;
v0x56338293e7d0_0 .var "tuop", 2 0;
TD_tile_decoder_pkg.get_instruction_length ;
    %alloc S_0x56338293a000;
    %load/vec4 v0x56338293e420_0;
    %store/vec4 v0x56338293a2c0_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.extract_ace_op, S_0x56338293a000;
    %free S_0x56338293a000;
    %store/vec4 v0x56338293e320_0, 0, 7;
    %load/vec4 v0x56338293e320_0;
    %cmpi/ne 123, 0, 7;
    %jmp/0xz  T_6.110, 4;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
T_6.110 ;
    %load/vec4 v0x56338293e420_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0x56338293e7d0_0, 0, 3;
    %load/vec4 v0x56338293e7d0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_6.112, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 3;
    %cmp/u;
    %jmp/1 T_6.113, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 3;
    %cmp/u;
    %jmp/1 T_6.114, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 3;
    %cmp/u;
    %jmp/1 T_6.115, 6;
    %dup/vec4;
    %pushi/vec4 4, 0, 3;
    %cmp/u;
    %jmp/1 T_6.116, 6;
    %dup/vec4;
    %pushi/vec4 5, 0, 3;
    %cmp/u;
    %jmp/1 T_6.117, 6;
    %dup/vec4;
    %pushi/vec4 6, 0, 3;
    %cmp/u;
    %jmp/1 T_6.118, 6;
    %dup/vec4;
    %pushi/vec4 7, 0, 3;
    %cmp/u;
    %jmp/1 T_6.119, 6;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.121;
T_6.112 ;
    %load/vec4 v0x56338293e420_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x56338293e6a0_0, 0, 6;
    %load/vec4 v0x56338293e420_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0x56338293e5c0_0, 0, 2;
    %load/vec4 v0x56338293e5c0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_6.122, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_6.123, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_6.124, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_6.125, 6;
    %jmp T_6.126;
T_6.122 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.126;
T_6.123 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.126;
T_6.124 ;
    %pushi/vec4 2, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.126;
T_6.125 ;
    %load/vec4 v0x56338293e6a0_0;
    %cmpi/e 28, 0, 6;
    %jmp/0xz  T_6.127, 4;
    %pushi/vec4 3, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.128;
T_6.127 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
T_6.128 ;
    %jmp T_6.126;
T_6.126 ;
    %pop/vec4 1;
    %jmp T_6.121;
T_6.113 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.121;
T_6.114 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.121;
T_6.115 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.121;
T_6.116 ;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.121;
T_6.117 ;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.121;
T_6.118 ;
    %load/vec4 v0x56338293e420_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0x56338293e5c0_0, 0, 2;
    %load/vec4 v0x56338293e420_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x56338293e6a0_0, 0, 6;
    %load/vec4 v0x56338293e6a0_0;
    %cmpi/e 1, 0, 6;
    %jmp/0xz  T_6.129, 4;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.130;
T_6.129 ;
    %load/vec4 v0x56338293e6a0_0;
    %cmpi/e 0, 0, 6;
    %jmp/0xz  T_6.131, 4;
    %load/vec4 v0x56338293e5c0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_6.133, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_6.134, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_6.135, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_6.136, 6;
    %jmp T_6.137;
T_6.133 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.137;
T_6.134 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.137;
T_6.135 ;
    %pushi/vec4 2, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.137;
T_6.136 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.137;
T_6.137 ;
    %pop/vec4 1;
    %jmp T_6.132;
T_6.131 ;
    %load/vec4 v0x56338293e6a0_0;
    %cmpi/e 28, 0, 6;
    %jmp/0xz  T_6.138, 4;
    %pushi/vec4 3, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.139;
T_6.138 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
T_6.139 ;
T_6.132 ;
T_6.130 ;
    %jmp T_6.121;
T_6.119 ;
    %load/vec4 v0x56338293e420_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x56338293e6a0_0, 0, 6;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x56338293e140;
    %jmp T_6.121;
T_6.121 ;
    %pop/vec4 1;
    %end;
S_0x56338293e8b0 .scope autofunction.vec4.s134, "init_collector" "init_collector" 3 121, 3 121 0, S_0x5633828bdc00;
 .timescale 0 0;
v0x56338293ea90_0 .var "collector", 133 0;
v0x56338293eb90_0 .var "first_word", 31 0;
; Variable init_collector is vec4 return value of scope S_0x56338293e8b0
TD_tile_decoder_pkg.init_collector ;
    %pushi/vec4 0, 0, 128;
    %ix/load 4, 6, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293ea90_0, 4, 128;
    %load/vec4 v0x56338293eb90_0;
    %ix/load 4, 6, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293ea90_0, 4, 32;
    %pushi/vec4 1, 0, 2;
    %ix/load 4, 4, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293ea90_0, 4, 2;
    %alloc S_0x56338293e140;
    %load/vec4 v0x56338293eb90_0;
    %store/vec4 v0x56338293e420_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.get_instruction_length, S_0x56338293e140;
    %free S_0x56338293e140;
    %ix/load 4, 2, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293ea90_0, 4, 2;
    %alloc S_0x56338293ed30;
    %load/vec4 v0x56338293eb90_0;
    %store/vec4 v0x56338293f0a0_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.is_tile_instruction, S_0x56338293ed30;
    %free S_0x56338293ed30;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293ea90_0, 4, 1;
    %load/vec4 v0x56338293ea90_0;
    %parti/u 2, 2, 32;
    %pushi/vec4 0, 0, 2;
    %cmp/e;
    %flag_get/vec4 4;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293ea90_0, 4, 1;
    %load/vec4 v0x56338293ea90_0;
    %ret/vec4 0, 0, 134;  Assign to init_collector (store_vec4_to_lval)
    %disable S_0x56338293e8b0;
    %end;
S_0x56338293ed30 .scope autofunction.vec4.s1, "is_tile_instruction" "is_tile_instruction" 3 33, 3 33 0, S_0x5633828bdc00;
 .timescale 0 0;
v0x56338293efa0_0 .var "ace_op", 6 0;
v0x56338293f0a0_0 .var "first_word", 31 0;
; Variable is_tile_instruction is vec4 return value of scope S_0x56338293ed30
TD_tile_decoder_pkg.is_tile_instruction ;
    %alloc S_0x56338293a000;
    %load/vec4 v0x56338293f0a0_0;
    %store/vec4 v0x56338293a2c0_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.extract_ace_op, S_0x56338293a000;
    %free S_0x56338293a000;
    %store/vec4 v0x56338293efa0_0, 0, 7;
    %load/vec4 v0x56338293efa0_0;
    %pushi/vec4 123, 0, 7;
    %cmp/e;
    %flag_get/vec4 4;
    %ret/vec4 0, 0, 1;  Assign to is_tile_instruction (store_vec4_to_lval)
    %disable S_0x56338293ed30;
    %end;
S_0x5633828c3080 .scope module, "test_ace_simplified" "test_ace_simplified" 4 4;
 .timescale 0 0;
S_0x56338293f250 .scope begin, "$unm_blk_55" "$unm_blk_55" 4 6, 4 6 0, S_0x5633828c3080;
 .timescale 0 0;
v0x56338293f400_0 .var "collector", 133 0;
v0x56338293f500_0 .var/str "disasm_result";
v0x56338293f5c0_0 .var "test_instruction", 31 0;
S_0x5633828c3210 .scope module, "tile_instruction_decoder_example" "tile_instruction_decoder_example" 3 467;
 .timescale 0 0;
S_0x56338293f6b0 .scope begin, "$unm_blk_50" "$unm_blk_50" 3 470, 3 470 0, S_0x5633828c3210;
 .timescale 0 0;
v0x56338293f8b0_0 .var "collector", 133 0;
v0x56338293f9b0_0 .var/str "disasm_result";
v0x56338293fa70_0 .var "test_word", 31 0;
    .scope S_0x5633828c3080;
T_9 ;
    %fork t_23, S_0x56338293f250;
    %jmp t_22;
    .scope S_0x56338293f250;
t_23 ;
    %vpi_call/w 4 11 "$display", "=== Testing simplified ACE instruction support ===" {0 0 0};
    %pushi/vec4 0, 0, 32;
    %store/vec4 v0x56338293f5c0_0, 0, 32;
    %pushi/vec4 123, 0, 7;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293f5c0_0, 4, 7;
    %pushi/vec4 7, 0, 3;
    %ix/load 4, 12, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293f5c0_0, 4, 3;
    %pushi/vec4 5, 0, 5;
    %ix/load 4, 15, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293f5c0_0, 4, 5;
    %pushi/vec4 0, 0, 3;
    %ix/load 4, 26, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293f5c0_0, 4, 3;
    %pushi/vec4 1, 0, 1;
    %ix/load 4, 31, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293f5c0_0, 4, 1;
    %vpi_call/w 4 23 "$display", "\012Testing ace_bsync instruction: 0x%08x", v0x56338293f5c0_0 {0 0 0};
    %vpi_call/w 4 24 "$display", "  sync_id = %d", &PV<v0x56338293f5c0_0, 15, 5> {0 0 0};
    %vpi_call/w 4 25 "$display", "  miscop = %b (should be 000 for ace_bsync)", &PV<v0x56338293f5c0_0, 26, 3> {0 0 0};
    %vpi_call/w 4 26 "$display", "  ace_misc_en = %b (should be 1)", &PV<v0x56338293f5c0_0, 31, 1> {0 0 0};
    %alloc S_0x56338293ed30;
    %load/vec4 v0x56338293f5c0_0;
    %store/vec4 v0x56338293f0a0_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.is_tile_instruction, S_0x56338293ed30;
    %free S_0x56338293ed30;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.0, 8;
    %vpi_call/w 4 29 "$display", "\342\234\223 Recognized as tile instruction" {0 0 0};
    %alloc S_0x56338293e8b0;
    %load/vec4 v0x56338293f5c0_0;
    %store/vec4 v0x56338293eb90_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x56338293e8b0;
    %free S_0x56338293e8b0;
    %store/vec4 v0x56338293f400_0, 0, 134;
    %vpi_call/w 4 32 "$display", "\342\234\223 Expected length: %0d (32-bit)", &PV<v0x56338293f400_0, 2, 2> {0 0 0};
    %load/vec4 v0x56338293f400_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_9.2, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_9.3, 8;
T_9.2 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_9.3, 8;
 ; End of false expr.
    %blend;
T_9.3;
    %vpi_call/w 4 33 "$display", "\342\234\223 Is complete: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x56338293f400_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.4, 8;
    %alloc S_0x563382939910;
    %load/vec4 v0x56338293f400_0;
    %parti/u 128, 6, 32;
    %load/vec4 v0x56338293f400_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x563382939d50_0, 0, 2;
    %store/vec4 v0x563382939c90_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x563382939910;
    %free S_0x563382939910;
    %store/str v0x56338293f500_0;
    %vpi_call/w 4 38 "$display", "\342\234\223 Disassembly: %s", v0x56338293f500_0 {0 0 0};
    %load/str v0x56338293f500_0;
    %pushi/str "ace_bsync x5";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.6, 8;
    %vpi_call/w 4 41 "$display", "\342\234\223 SUCCESS: ace_bsync correctly identified with sync_id" {0 0 0};
    %jmp T_9.7;
T_9.6 ;
    %vpi_call/w 4 43 "$display", "\342\234\227 FAILURE: Expected 'ace_bsync x5', got '%s'", v0x56338293f500_0 {0 0 0};
T_9.7 ;
T_9.4 ;
    %jmp T_9.1;
T_9.0 ;
    %vpi_call/w 4 47 "$display", "\342\234\227 FAILURE: Not recognized as tile instruction" {0 0 0};
T_9.1 ;
    %pushi/vec4 0, 0, 32;
    %store/vec4 v0x56338293f5c0_0, 0, 32;
    %pushi/vec4 123, 0, 7;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293f5c0_0, 4, 7;
    %pushi/vec4 7, 0, 3;
    %ix/load 4, 12, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293f5c0_0, 4, 3;
    %pushi/vec4 10, 0, 5;
    %ix/load 4, 15, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293f5c0_0, 4, 5;
    %pushi/vec4 2, 0, 3;
    %ix/load 4, 26, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293f5c0_0, 4, 3;
    %pushi/vec4 1, 0, 1;
    %ix/load 4, 31, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293f5c0_0, 4, 1;
    %vpi_call/w 4 58 "$display", "\012Testing ace_nbsync instruction: 0x%08x", v0x56338293f5c0_0 {0 0 0};
    %vpi_call/w 4 59 "$display", "  sync_id = %d", &PV<v0x56338293f5c0_0, 15, 5> {0 0 0};
    %vpi_call/w 4 60 "$display", "  miscop = %b (should be 010 for ace_nbsync)", &PV<v0x56338293f5c0_0, 26, 3> {0 0 0};
    %vpi_call/w 4 61 "$display", "  ace_misc_en = %b (should be 1)", &PV<v0x56338293f5c0_0, 31, 1> {0 0 0};
    %alloc S_0x56338293ed30;
    %load/vec4 v0x56338293f5c0_0;
    %store/vec4 v0x56338293f0a0_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.is_tile_instruction, S_0x56338293ed30;
    %free S_0x56338293ed30;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.8, 8;
    %vpi_call/w 4 64 "$display", "\342\234\223 Recognized as tile instruction" {0 0 0};
    %alloc S_0x56338293e8b0;
    %load/vec4 v0x56338293f5c0_0;
    %store/vec4 v0x56338293eb90_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x56338293e8b0;
    %free S_0x56338293e8b0;
    %store/vec4 v0x56338293f400_0, 0, 134;
    %vpi_call/w 4 67 "$display", "\342\234\223 Expected length: %0d (32-bit)", &PV<v0x56338293f400_0, 2, 2> {0 0 0};
    %load/vec4 v0x56338293f400_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_9.10, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_9.11, 8;
T_9.10 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_9.11, 8;
 ; End of false expr.
    %blend;
T_9.11;
    %vpi_call/w 4 68 "$display", "\342\234\223 Is complete: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x56338293f400_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.12, 8;
    %alloc S_0x563382939910;
    %load/vec4 v0x56338293f400_0;
    %parti/u 128, 6, 32;
    %load/vec4 v0x56338293f400_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x563382939d50_0, 0, 2;
    %store/vec4 v0x563382939c90_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x563382939910;
    %free S_0x563382939910;
    %store/str v0x56338293f500_0;
    %vpi_call/w 4 73 "$display", "\342\234\223 Disassembly: %s", v0x56338293f500_0 {0 0 0};
    %load/str v0x56338293f500_0;
    %pushi/str "ace_nbsync x10";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.14, 8;
    %vpi_call/w 4 76 "$display", "\342\234\223 SUCCESS: ace_nbsync correctly identified with sync_id" {0 0 0};
    %jmp T_9.15;
T_9.14 ;
    %vpi_call/w 4 78 "$display", "\342\234\227 FAILURE: Expected 'ace_nbsync x10', got '%s'", v0x56338293f500_0 {0 0 0};
T_9.15 ;
T_9.12 ;
    %jmp T_9.9;
T_9.8 ;
    %vpi_call/w 4 82 "$display", "\342\234\227 FAILURE: Not recognized as tile instruction" {0 0 0};
T_9.9 ;
    %pushi/vec4 0, 0, 32;
    %store/vec4 v0x56338293f5c0_0, 0, 32;
    %pushi/vec4 123, 0, 7;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293f5c0_0, 4, 7;
    %pushi/vec4 7, 0, 3;
    %ix/load 4, 12, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293f5c0_0, 4, 3;
    %pushi/vec4 1, 0, 5;
    %ix/load 4, 15, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293f5c0_0, 4, 5;
    %pushi/vec4 1, 0, 3;
    %ix/load 4, 26, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293f5c0_0, 4, 3;
    %pushi/vec4 1, 0, 1;
    %ix/load 4, 31, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x56338293f5c0_0, 4, 1;
    %vpi_call/w 4 93 "$display", "\012Testing unknown ACE instruction: 0x%08x", v0x56338293f5c0_0 {0 0 0};
    %alloc S_0x56338293ed30;
    %load/vec4 v0x56338293f5c0_0;
    %store/vec4 v0x56338293f0a0_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.is_tile_instruction, S_0x56338293ed30;
    %free S_0x56338293ed30;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.16, 8;
    %alloc S_0x56338293e8b0;
    %load/vec4 v0x56338293f5c0_0;
    %store/vec4 v0x56338293eb90_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x56338293e8b0;
    %free S_0x56338293e8b0;
    %store/vec4 v0x56338293f400_0, 0, 134;
    %load/vec4 v0x56338293f400_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.18, 8;
    %alloc S_0x563382939910;
    %load/vec4 v0x56338293f400_0;
    %parti/u 128, 6, 32;
    %load/vec4 v0x56338293f400_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x563382939d50_0, 0, 2;
    %store/vec4 v0x563382939c90_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x563382939910;
    %free S_0x563382939910;
    %store/str v0x56338293f500_0;
    %vpi_call/w 4 100 "$display", "\342\234\223 Disassembly: %s", v0x56338293f500_0 {0 0 0};
    %load/str v0x56338293f500_0;
    %pushi/str "unknown_ace_misc 0";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.20, 8;
    %vpi_call/w 4 103 "$display", "\342\234\223 SUCCESS: Unknown ACE instruction correctly handled" {0 0 0};
    %jmp T_9.21;
T_9.20 ;
    %vpi_call/w 4 105 "$display", "  Note: Got '%s' (acceptable for unknown instruction)", v0x56338293f500_0 {0 0 0};
T_9.21 ;
T_9.18 ;
T_9.16 ;
    %vpi_call/w 4 110 "$display", "\012=== Regression test - ensure other instructions still work ===" {0 0 0};
    %pushi/vec4 1887457403, 0, 32;
    %store/vec4 v0x56338293f5c0_0, 0, 32;
    %vpi_call/w 4 114 "$display", "Testing twait instruction: 0x%08x", v0x56338293f5c0_0 {0 0 0};
    %alloc S_0x56338293e8b0;
    %load/vec4 v0x56338293f5c0_0;
    %store/vec4 v0x56338293eb90_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x56338293e8b0;
    %free S_0x56338293e8b0;
    %store/vec4 v0x56338293f400_0, 0, 134;
    %load/vec4 v0x56338293f400_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.22, 8;
    %alloc S_0x563382939910;
    %load/vec4 v0x56338293f400_0;
    %parti/u 128, 6, 32;
    %load/vec4 v0x56338293f400_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x563382939d50_0, 0, 2;
    %store/vec4 v0x563382939c90_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x563382939910;
    %free S_0x563382939910;
    %store/str v0x56338293f500_0;
    %vpi_call/w 4 119 "$display", "  Result: %s", v0x56338293f500_0 {0 0 0};
    %load/str v0x56338293f500_0;
    %pushi/str "twait";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.24, 8;
    %vpi_call/w 4 121 "$display", "\342\234\223 twait instruction still works" {0 0 0};
T_9.24 ;
T_9.22 ;
    %pushi/vec4 16507, 0, 32;
    %store/vec4 v0x56338293f5c0_0, 0, 32;
    %vpi_call/w 4 127 "$display", "Testing CSR instruction: 0x%08x", v0x56338293f5c0_0 {0 0 0};
    %alloc S_0x56338293e8b0;
    %load/vec4 v0x56338293f5c0_0;
    %store/vec4 v0x56338293eb90_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x56338293e8b0;
    %free S_0x56338293e8b0;
    %store/vec4 v0x56338293f400_0, 0, 134;
    %load/vec4 v0x56338293f400_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.26, 8;
    %alloc S_0x563382939910;
    %load/vec4 v0x56338293f400_0;
    %parti/u 128, 6, 32;
    %load/vec4 v0x56338293f400_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x563382939d50_0, 0, 2;
    %store/vec4 v0x563382939c90_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x563382939910;
    %free S_0x563382939910;
    %store/str v0x56338293f500_0;
    %vpi_call/w 4 132 "$display", "  Result: %s", v0x56338293f500_0 {0 0 0};
    %load/str v0x56338293f500_0;
    %pushi/str "tcsrw.i 0";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.28, 8;
    %vpi_call/w 4 134 "$display", "\342\234\223 CSR instruction still works" {0 0 0};
T_9.28 ;
T_9.26 ;
    %vpi_call/w 4 138 "$display", "\012=== Summary ===" {0 0 0};
    %vpi_call/w 4 139 "$display", "\342\234\223 Simplified ACE instruction support" {0 0 0};
    %vpi_call/w 4 140 "$display", "\342\234\223 Only ace_bsync and ace_nbsync are supported" {0 0 0};
    %vpi_call/w 4 141 "$display", "\342\234\223 Removed complex memory operation parsing" {0 0 0};
    %vpi_call/w 4 142 "$display", "\342\234\223 Maintained compatibility with existing instructions" {0 0 0};
    %vpi_call/w 4 144 "$display", "\012=== Test Complete ===" {0 0 0};
    %end;
    .scope S_0x5633828c3080;
t_22 %join;
    %end;
    .thread T_9;
    .scope S_0x5633828c3210;
T_10 ;
    %fork t_25, S_0x56338293f6b0;
    %jmp t_24;
    .scope S_0x56338293f6b0;
t_25 ;
    %vpi_call/w 3 475 "$display", "=== Tile Instruction Decoder Example ===" {0 0 0};
    %pushi/vec4 16507, 0, 32;
    %store/vec4 v0x56338293fa70_0, 0, 32;
    %vpi_call/w 3 479 "$display", "\012Testing 32-bit instruction: 0x%08x", v0x56338293fa70_0 {0 0 0};
    %alloc S_0x56338293ed30;
    %load/vec4 v0x56338293fa70_0;
    %store/vec4 v0x56338293f0a0_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.is_tile_instruction, S_0x56338293ed30;
    %free S_0x56338293ed30;
    %flag_set/vec4 8;
    %jmp/0xz  T_10.0, 8;
    %alloc S_0x56338293e8b0;
    %load/vec4 v0x56338293fa70_0;
    %store/vec4 v0x56338293eb90_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x56338293e8b0;
    %free S_0x56338293e8b0;
    %store/vec4 v0x56338293f8b0_0, 0, 134;
    %load/vec4 v0x56338293f8b0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_10.2, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_10.3, 8;
T_10.2 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_10.3, 8;
 ; End of false expr.
    %blend;
T_10.3;
    %vpi_call/w 3 483 "$display", "  Is tile: YES, Length: %0d, Complete: %s", &PV<v0x56338293f8b0_0, 2, 2>, S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x56338293f8b0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_10.4, 8;
    %alloc S_0x563382939910;
    %load/vec4 v0x56338293f8b0_0;
    %parti/u 128, 6, 32;
    %load/vec4 v0x56338293f8b0_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x563382939d50_0, 0, 2;
    %store/vec4 v0x563382939c90_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x563382939910;
    %free S_0x563382939910;
    %store/str v0x56338293f9b0_0;
    %vpi_call/w 3 490 "$display", "  Disassembly: %s", v0x56338293f9b0_0 {0 0 0};
T_10.4 ;
T_10.0 ;
    %vpi_call/w 3 495 "$display", "\012Testing 64-bit instruction:" {0 0 0};
    %pushi/vec4 24699, 0, 32;
    %store/vec4 v0x56338293fa70_0, 0, 32;
    %vpi_call/w 3 497 "$display", "  Word 1: 0x%08x", v0x56338293fa70_0 {0 0 0};
    %alloc S_0x56338293e8b0;
    %load/vec4 v0x56338293fa70_0;
    %store/vec4 v0x56338293eb90_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x56338293e8b0;
    %free S_0x56338293e8b0;
    %store/vec4 v0x56338293f8b0_0, 0, 134;
    %load/vec4 v0x56338293f8b0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_10.6, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_10.7, 8;
T_10.6 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_10.7, 8;
 ; End of false expr.
    %blend;
T_10.7;
    %vpi_call/w 3 500 "$display", "  Expected length: %0d, Complete: %s", &PV<v0x56338293f8b0_0, 2, 2>, S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x56338293f8b0_0;
    %parti/u 1, 1, 32;
    %nor/r;
    %flag_set/vec4 8;
    %jmp/0xz  T_10.8, 8;
    %pushi/vec4 2147483771, 0, 32;
    %store/vec4 v0x56338293fa70_0, 0, 32;
    %vpi_call/w 3 506 "$display", "  Word 2: 0x%08x", v0x56338293fa70_0 {0 0 0};
    %alloc S_0x5633828c33a0;
    %load/vec4 v0x56338293f8b0_0;
    %load/vec4 v0x56338293fa70_0;
    %store/vec4 v0x563382939830_0, 0, 32;
    %store/vec4 v0x563382939690_0, 0, 134;
    %callf/vec4 TD_tile_decoder_pkg.add_word_to_collector, S_0x5633828c33a0;
    %free S_0x5633828c33a0;
    %store/vec4 v0x56338293f8b0_0, 0, 134;
    %load/vec4 v0x56338293f8b0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_10.10, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_10.11, 8;
T_10.10 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_10.11, 8;
 ; End of false expr.
    %blend;
T_10.11;
    %vpi_call/w 3 508 "$display", "  Complete: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x56338293f8b0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_10.12, 8;
    %alloc S_0x563382939910;
    %load/vec4 v0x56338293f8b0_0;
    %parti/u 128, 6, 32;
    %load/vec4 v0x56338293f8b0_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x563382939d50_0, 0, 2;
    %store/vec4 v0x563382939c90_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x563382939910;
    %free S_0x563382939910;
    %store/str v0x56338293f9b0_0;
    %vpi_call/w 3 513 "$display", "  Disassembly: %s", v0x56338293f9b0_0 {0 0 0};
T_10.12 ;
T_10.8 ;
    %vpi_call/w 3 517 "$display", "\012=== Example Complete ===" {0 0 0};
    %end;
    .scope S_0x5633828c3210;
t_24 %join;
    %end;
    .thread T_10;
# The file index is used to find the file name in the following table.
:file_names 5;
    "N/A";
    "<interactive>";
    "-";
    "tile_instruction_decoder.sv";
    "test_ace_simplified.sv";
