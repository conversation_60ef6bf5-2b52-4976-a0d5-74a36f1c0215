

cpu {
  name = AX45MPV;
  flen = 64;
  elen = 32;
  felen = 32;
  vlen = 1024;
  dlen = 512;
  streaming_port_width = 512;
};


config {
    custom_error_en = no;
    export_level_port = 4;
    export_level_streaming_port = 4;
    group_in_buffer = 2;
};

// -- Command channel
port cmdValid {
    export_level = 4;
    width = 1;
    io_type = out;
};

port cmdInfo {
    export_level = 4;
    width = 209;
    io_type = out;
};

port cmdReady {
    export_level = 4;
    width = 1;
    io_type = in;
};

port aceRd {
    export_level = 4;
    width = 64;
    io_type = in;
};

port rdValid {
    export_level = 4;
    width = 1;
    io_type = in;
};

port rdReady {
    export_level = 4;
    width = 1;
    io_type = out;
};

port ace_tfe_reset {
    width = 1;
    io_type = out;
};

port tfe_ace_insts {
    width = 2;
    io_type = in;
};

port tsync_vld {
    width = 1;
    io_type = out;
};

port tsync_info {
    width = 7;
    io_type = out;
};

port tsync_release {
    width = 1;
    io_type = in;
};

port tsync_error {
    width = 1;
    io_type = in;
};


port tacp_cnt {
    width = 8;
    io_type = in;
};

port tgld_cnt {
    width = 8;
    io_type = in;
};

port tgst_cnt {
    width = 8;
    io_type = in;
};

port tsld_cnt {
    width = 8;
    io_type = in;
};

port tsst_cnt {
    width = 8;
    io_type = in;
};

// -- Load data channel: VPU<->External device, not in ACE
// -- Store data channel: VPU<->External device, not in ACE

// -- Status channel
// 0: IDLE
// 1: BUSY
port tile_core_idle {
    width = 1;
    io_type = in;
};

port tile_mem_idle {
    // tgld_done/tgst_done/tsld_done/tsst_done
    width = 4;
    io_type = in;
};

// -- address control register
// address is assumed byte-aligned
// | bit position | content |
// | [19:0]       | address |
// | [39:20]      | mask    |
// | [55:40]      | stride  |
reg addrCtl {
    width = 1;
    num = 1;
    util = {none};
};

insn ace_mem_high_rs1_0_rd_0_rs4_0 {
  op = {imm4 op0:4:9, imm3 op1:3:20, imm3 op2:3:28, imm5 op3:5:15, imm5 op4:5:23, 0:1:31|2:13|2:7};
  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |=(((cmdInfo_t)0)<<0);
cmdInfo |=(((cmdInfo_t)0)<<1);
cmdInfo |= (((cmdInfo_t)op0)<<2);
cmdInfo |= (((cmdInfo_t)1)<<25);
cmdInfo |=(((cmdInfo_t)0)<<6);
cmdInfo |= (((cmdInfo_t)op3)<<8);
cmdInfo |= (((cmdInfo_t)op1)<<13);
cmdInfo |= (((cmdInfo_t)op4)<<16);
cmdInfo |= (((cmdInfo_t)op2)<<21);
cmdInfo |=(((cmdInfo_t)0)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);
  %};
};

insn ace_mem_high_rs1_0_rd_0_rs4_1 {
  op = {imm4 op0:4:9, imm3 op1:3:20, imm3 op2:3:28, imm5 op3:5:15, in xrf rs4:5:23, 256:1:31|2:13|2:7};
  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |=(((cmdInfo_t)0)<<0);
cmdInfo |=(((cmdInfo_t)1)<<1);
cmdInfo |= (((cmdInfo_t)op0)<<2);
cmdInfo |= (((cmdInfo_t)1)<<25);
cmdInfo |=(((cmdInfo_t)0)<<6);
cmdInfo |= (((cmdInfo_t)op3)<<8);
cmdInfo |= (((cmdInfo_t)op1)<<13);
cmdInfo |= (((cmdInfo_t)rs4)<<96);
cmdInfo |= (((cmdInfo_t)1)<<30);
cmdInfo |= (((cmdInfo_t)op2)<<21);
cmdInfo |=(((cmdInfo_t)0)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);
  %};
};


insn ace_mem_high_rs1_0_rd_1 {
  op = {imm5 op0:5:8, imm3 op1:3:20, imm3 op2:3:28, imm5 op3:5:15, out xrf rd:5:23, 128:1:31|2:13|1:7};
  implied_op = {cmdInfo, cmdValid, cmdReady, aceRd, rdValid, rdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |=(((cmdInfo_t)1)<<0);
cmdInfo |= (((cmdInfo_t)op0)<<1);
cmdInfo |= (((cmdInfo_t)1)<<25);
cmdInfo |=(((cmdInfo_t)0)<<6);
cmdInfo |= (((cmdInfo_t)op3)<<8);
cmdInfo |= (((cmdInfo_t)op1)<<13);
cmdInfo |= (((cmdInfo_t)op2)<<21);
cmdInfo |=(((cmdInfo_t)0)<<24);
cmdInfo |= (((cmdInfo_t)1)<<31);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);
    while (!acp_read_rdValid()) {
      ace_advance_cycle(1);
    }
    aceRd = acp_read_aceRd();
    rd = aceRd;
    acp_write_rdReady(1);
    ace_advance_cycle(1);
    acp_sc_write_rdReady(0);
  %};
};

insn ace_mem_high_rs1_1_rd_0_rs4_0 {
  op = {imm5 op0:5:9, imm3 op1:3:20, imm3 op2:3:28, in xrf rs1:5:15, imm5 op4:5:23, 2147483648:1:31|2:13|2:7};
  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |=(((cmdInfo_t)0)<<0);
cmdInfo |=(((cmdInfo_t)0)<<1);
cmdInfo |= (((cmdInfo_t)op0)<<2);
cmdInfo |= (((cmdInfo_t)1)<<25);
cmdInfo |=(((cmdInfo_t)0)<<6);
cmdInfo |= (((cmdInfo_t)rs1)<<32);
cmdInfo |= (((cmdInfo_t)1)<<29);
cmdInfo |= (((cmdInfo_t)op1)<<13);
cmdInfo |= (((cmdInfo_t)op4)<<16);
cmdInfo |= (((cmdInfo_t)op2)<<21);
cmdInfo |=(((cmdInfo_t)1)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);
  %};
};

insn ace_mem_high_rs1_1_rd_0_rs4_1 {
  op = {imm4 op0:4:9, imm3 op1:3:20, imm3 op2:3:28, in xrf rs1:5:15, in xrf rs4:5:23, 2147483904:1:31|2:13|2:7};
  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |=(((cmdInfo_t)0)<<0);
cmdInfo |=(((cmdInfo_t)1)<<1);
cmdInfo |= (((cmdInfo_t)op0)<<2);
cmdInfo |= (((cmdInfo_t)1)<<25);
cmdInfo |=(((cmdInfo_t)0)<<6);
cmdInfo |= (((cmdInfo_t)rs1)<<32);
cmdInfo |= (((cmdInfo_t)1)<<29);
cmdInfo |= (((cmdInfo_t)op1)<<13);
cmdInfo |= (((cmdInfo_t)rs4)<<96);
cmdInfo |= (((cmdInfo_t)1)<<30);
cmdInfo |= (((cmdInfo_t)op2)<<21);
cmdInfo |=(((cmdInfo_t)1)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);
  %};
};


insn ace_mem_high_rs1_1_rd_1 {
  op = {imm5 op0:5:8, imm3 op1:3:20, imm3 op2:3:28, in xrf rs1:5:15, out xrf rd:5:23, 2147483776:1:31|2:13|1:7};
  implied_op = {cmdInfo, cmdValid, cmdReady,aceRd, rdValid, rdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |=(((cmdInfo_t)1)<<0);
cmdInfo |= (((cmdInfo_t)op0)<<1);
cmdInfo |= (((cmdInfo_t)1)<<25);
cmdInfo |=(((cmdInfo_t)0)<<6);
cmdInfo |= (((cmdInfo_t)rs1)<<32);
cmdInfo |= (((cmdInfo_t)1)<<29);
cmdInfo |= (((cmdInfo_t)op1)<<13);
cmdInfo |= (((cmdInfo_t)op2)<<21);
cmdInfo |=(((cmdInfo_t)1)<<24);
cmdInfo |= (((cmdInfo_t)1)<<31);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);
    while (!acp_read_rdValid()) {
      ace_advance_cycle(1);
    }
    aceRd = acp_read_aceRd();
    rd = aceRd;
    acp_write_rdReady(1);
    ace_advance_cycle(1);
    acp_sc_write_rdReady(0);
  %};
};

insn ace_mma_high_rs1_0 {
  op = {imm4 op0:4:8, imm8 Ts1:8:15, imm8 Td:8:23, 12288:1:31|3:12|1:7};
  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |=(((cmdInfo_t)0)<<0);
cmdInfo |= (((cmdInfo_t)op0)<<1);
cmdInfo |= (((cmdInfo_t)1)<<25);
cmdInfo |=(((cmdInfo_t)3)<<5);
cmdInfo |= (((cmdInfo_t)Ts1)<<8);
cmdInfo |= (((cmdInfo_t)Td)<<16);
cmdInfo |=(((cmdInfo_t)0)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);
  %};
};

insn ace_mma_high_rs1_1 {
  op = {imm4 op0:4:8, in xrf rs1:5:15, imm8 Td:8:23, 2147495936:1:31|3:20|3:12|1:7};
  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |=(((cmdInfo_t)0)<<0);
cmdInfo |= (((cmdInfo_t)op0)<<1);
cmdInfo |= (((cmdInfo_t)1)<<25);
cmdInfo |=(((cmdInfo_t)3)<<5);
cmdInfo |= (((cmdInfo_t)rs1)<<32);
cmdInfo |= (((cmdInfo_t)1)<<29);
cmdInfo |=(((cmdInfo_t)0)<<13);
cmdInfo |= (((cmdInfo_t)Td)<<16);
cmdInfo |=(((cmdInfo_t)1)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);
  %};
};


insn ace_alu_high_rs3_0_inst3_1 {
  op = {imm4 op0:4:8, imm8 Ts1:8:15, imm8 Td:8:23, 8320:1:31|3:12|1:7};
  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |=(((cmdInfo_t)1)<<0);
cmdInfo |= (((cmdInfo_t)op0)<<1);
cmdInfo |= (((cmdInfo_t)1)<<25);
cmdInfo |=(((cmdInfo_t)2)<<5);
cmdInfo |= (((cmdInfo_t)Ts1)<<8);
cmdInfo |= (((cmdInfo_t)Td)<<16);
cmdInfo |=(((cmdInfo_t)0)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);
  %};
};

insn ace_alu_high_rs3_0_inst3_0 {
//  op = {imm4 op0:4:8, imm8 ts1_ts3:8:15, imm7 op2:7:24, 128:1:31|4:20|3:12|1:7};
  op = {imm4 op0:4:8, imm8 Ts1_Ts3:8:15, imm7 op2:8:23, 8192:1:31|3:12|1:7};

  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |=(((cmdInfo_t)0)<<0);
cmdInfo |= (((cmdInfo_t)op0)<<1);
cmdInfo |= (((cmdInfo_t)1)<<25);
cmdInfo |=(((cmdInfo_t)2)<<5);
cmdInfo |= (((cmdInfo_t)Ts1_Ts3)<<8);
cmdInfo |= (((cmdInfo_t)op2)<<16);
cmdInfo |=(((cmdInfo_t)0)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);
  %};
};

insn ace_alu_high_rs3_1 {
//  op = {imm4 op0:4:8, imm8 ts1_ts3:8:15, imm7 op2:8:23, in xrf rs3:5:15, 8192:1:31|3:20|3:12|1:7};
  op = {imm4 op0:4:8,  imm7 op2:8:23, in xrf rs3:5:15, 2147491840:1:31|3:12|1:7};
  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |=(((cmdInfo_t)0)<<0);
cmdInfo |= (((cmdInfo_t)op0)<<1);
cmdInfo |= (((cmdInfo_t)1)<<25);
cmdInfo |=(((cmdInfo_t)2)<<5);
cmdInfo |= (((cmdInfo_t)rs3)<<96);
cmdInfo |= (((cmdInfo_t)1)<<30);
cmdInfo |=(((cmdInfo_t)0)<<13);
cmdInfo |= (((cmdInfo_t)op2)<<16);
cmdInfo |=(((cmdInfo_t)1)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);
  %};
};

insn ace_low_rs2_0_rs3_0 {
  op = {imm4 op0:4:8, imm6 op1:6:25, imm5 op2:5:15, imm5 op3:5:20, 24576:1:31|3:12|1:7};
  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |=(((cmdInfo_t)0)<<0);
cmdInfo |= (((cmdInfo_t)op0)<<1);
cmdInfo |= (((cmdInfo_t)0)<<25);
cmdInfo |=(((cmdInfo_t)6)<<5);
cmdInfo |= (((cmdInfo_t)op2)<<8);
cmdInfo |= (((cmdInfo_t)op3)<<13);
cmdInfo |= (((cmdInfo_t)op1)<<18);
cmdInfo |=(((cmdInfo_t)0)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);
  %};
};

insn ace_low_rs2_1_rs3_0 {
  op = {imm4 op0:4:8, imm6 op1:6:25, in xrf rs2:5:15, imm5 op3:5:20, 24704:1:31|3:12|1:7};
  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |=(((cmdInfo_t)1)<<0);
cmdInfo |= (((cmdInfo_t)op0)<<1);
cmdInfo |= (((cmdInfo_t)0)<<25);
cmdInfo |=(((cmdInfo_t)6)<<5);
cmdInfo |= (((cmdInfo_t)rs2)<<32);
cmdInfo |= (((cmdInfo_t)1)<<29);
cmdInfo |= (((cmdInfo_t)op3)<<13);
cmdInfo |= (((cmdInfo_t)op1)<<18);
cmdInfo |=(((cmdInfo_t)0)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);
  %};
};

insn ace_low_rs2_0_rs3_1 {
  op = {imm4 op0:4:8, imm6 op1:6:25, imm5 op2:5:15, in xrf rs3:5:20, 2147508224:1:31|3:12|1:7};
  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |=(((cmdInfo_t)0)<<0);
cmdInfo |= (((cmdInfo_t)op0)<<1);
cmdInfo |= (((cmdInfo_t)0)<<25);
cmdInfo |=(((cmdInfo_t)6)<<5);
cmdInfo |= (((cmdInfo_t)op2)<<8);
cmdInfo |= (((cmdInfo_t)rs3)<<96);
cmdInfo |= (((cmdInfo_t)1)<<30);
cmdInfo |= (((cmdInfo_t)op1)<<18);
cmdInfo |=(((cmdInfo_t)1)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);
  %};
};

insn ace_low_rs2_1_rs3_1 {
  op = {imm4 op0:4:8, imm6 op1:6:25, in xrf rs2:5:15, in xrf rs3:5:20, 2147508352:1:31|3:12|1:7};
  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |=(((cmdInfo_t)1)<<0);
cmdInfo |= (((cmdInfo_t)op0)<<1);
cmdInfo |= (((cmdInfo_t)0)<<25);
cmdInfo |=(((cmdInfo_t)6)<<5);
cmdInfo |= (((cmdInfo_t)rs2)<<32);
cmdInfo |= (((cmdInfo_t)1)<<29);
cmdInfo |= (((cmdInfo_t)rs3)<<96);
cmdInfo |= (((cmdInfo_t)1)<<30);
cmdInfo |= (((cmdInfo_t)op1)<<18);
cmdInfo |=(((cmdInfo_t)1)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);
  %};
};

insn tcsrw_r {
  op = {imm5 op2:5:7, in xrf rs1:5:15, imm9 csr_addr:9:20, imm1 b64:1:29, 2147500032:2:30|3:12};
  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
    cmdInfo |=(((cmdInfo_t)0)<<0);
cmdInfo |= (((cmdInfo_t)2)<<25);
cmdInfo |=(((cmdInfo_t)4)<<5);
cmdInfo |= (((cmdInfo_t)rs1)<<32);
cmdInfo |= (((cmdInfo_t)1)<<29);
cmdInfo |= (((cmdInfo_t)csr_addr)<<13);
cmdInfo |= (((cmdInfo_t)b64)<<22);
cmdInfo |=(((cmdInfo_t)2)<<23);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    acp_sc_write_cmdValid(0);

  %};
};


insn tcsrr_r {
  op = {out xrf rd:5:7, imm5 op1:5:15, imm9 csr_addr:9:20, imm1 b64:1:29, 1073758208:2:30|3:12};
  implied_op = {cmdInfo, cmdValid, cmdReady,aceRd, rdValid, rdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
    cmdInfo |= (((cmdInfo_t)2)<<25);
cmdInfo |=(((cmdInfo_t)4)<<5);
cmdInfo |=(((cmdInfo_t)0)<<8);
cmdInfo |= (((cmdInfo_t)csr_addr)<<13);
cmdInfo |= (((cmdInfo_t)b64)<<22);
cmdInfo |=(((cmdInfo_t)1)<<23);
cmdInfo |= (((cmdInfo_t)1)<<31);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    acp_sc_write_cmdValid(0);
    while (!acp_read_rdValid()) {
      ace_advance_cycle(1);
    }
    aceRd = acp_read_aceRd();
    rd = aceRd;
    acp_write_rdReady(1);
    ace_advance_cycle(1);
    acp_sc_write_rdReady(0);
  %};
};

insn tcsrw_i {
  op = {imm5 op2:5:7, imm5 imm_1:5:15, imm9 csr_addr:9:20, imm1 b64:1:29, 16384:2:30|3:12};
  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
    cmdInfo |=(((cmdInfo_t)0)<<0);
cmdInfo |= (((cmdInfo_t)2)<<25);
cmdInfo |=(((cmdInfo_t)4)<<5);
cmdInfo |= (((cmdInfo_t)imm_1)<<8);
cmdInfo |= (((cmdInfo_t)csr_addr)<<13);
cmdInfo |= (((cmdInfo_t)b64)<<22);
cmdInfo |=(((cmdInfo_t)0)<<23);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    acp_sc_write_cmdValid(0);

  %};
};

insn twait_i_ls {
  op = {imm8 cnt:8:15, imm1 isStore:1:27, imm1 isShare:1:26, 8409088:4:28|3:23|8:7};
  implied_op = {cmdInfo, cmdValid, cmdReady,tgld_cnt,tgst_cnt,tsld_cnt,tsst_cnt};
  //csr_op= {pc, vl, vtype};
  csim = %{
    ace_advance_cycle(1);  
    ace_advance_cycle(1);  
    ace_advance_cycle(1);  
    ace_advance_cycle(1);  
    ace_advance_cycle(1);  
    ace_advance_cycle(1);  
    ace_advance_cycle(1);  
    if(isShare){
      if(isStore){
        while(tsst_cnt > cnt){
          ace_advance_cycle(1);  
        }
      } else {
        while(tsld_cnt > cnt){
          ace_advance_cycle(1);  
        }
      }
    } else {
      if(isStore){
        while(tgst_cnt > cnt){
          ace_advance_cycle(1);  
        }
      } else {
        while(tgld_cnt > cnt){
          ace_advance_cycle(1);  
        }
      }
    }

  %};
};

insn twait_r_ls {
  op = {in xrf rs1:5:15, imm1 isStore:1:27, imm1 isShare:1:26, 276844544:4:28|6:20|8:7};
  implied_op = {cmdInfo, cmdValid, cmdReady,tgld_cnt,tgst_cnt,tsld_cnt,tsst_cnt};
  //csr_op= {pc, vl, vtype};
  csim = %{
    ace_advance_cycle(1);  
    ace_advance_cycle(1);  
    ace_advance_cycle(1);  
    ace_advance_cycle(1);  
    ace_advance_cycle(1);  
    ace_advance_cycle(1);  
    ace_advance_cycle(1);  
    if(isShare){
      if(isStore){
        while(tsst_cnt > rs1){
          ace_advance_cycle(1);  
        }
      } else {
        while(tsld_cnt > rs1){
          ace_advance_cycle(1);  
        }
      }
    } else {
      if(isStore){
        while(tgst_cnt > rs1){
          ace_advance_cycle(1);  
        }
      } else {
        while(tgld_cnt > rs1){
          ace_advance_cycle(1);  
        }
      }
    }

  %};
};


insn twait_r_tacp_cg {
  op = {in xrf rs1:5:15, 545280000:4:28|2:26|3:23|3:20|8:7};
  implied_op = {cmdInfo, cmdValid, cmdReady,tacp_cnt};
  //csr_op= {pc, vl, vtype};
  csim = %{
      ace_advance_cycle(1);  
      ace_advance_cycle(1);  
      ace_advance_cycle(1);  
      ace_advance_cycle(1);  
      ace_advance_cycle(1);  
      ace_advance_cycle(1);  
      ace_advance_cycle(1);  
      while(tacp_cnt > rs1){
        ace_advance_cycle(1);  
      }

  %};
};

insn twait {

  op = {imm1 isMem:1:26, 1887457280:4:28|1:27|3:23|8:15|8:7};
  implied_op = {cmdInfo, cmdValid, cmdReady,tile_core_idle,tile_mem_idle};

  //csr_op= {pc, vl, vtype};
  csim = %{
      ace_advance_cycle(1);  
      ace_advance_cycle(1);  
      ace_advance_cycle(1);  
      ace_advance_cycle(1);  
      ace_advance_cycle(1);  
  if(isMem){
    while(!acp_read_tile_mem_idle()){
      ace_advance_cycle(1);  
    }
  } else {
    while(!acp_read_tile_core_idle()){
      ace_advance_cycle(1);  
    }
  }
  %};
};



insn tmv_vt_high {
  op = {out vrf data:5:7, io addrCtl addrCtl:1:26, func6 func:6:20, imm1 bc:1:19, 536899584:5:27|7:12};
  implied_op= {cmdInfo, cmdValid, cmdReady};
  csr_op= {pc, vl, vtype};
  streaming_port = load;
  csim= %{
    // addrCtl
    //  | bit position | content |
    //  | [19:0]       | address |
    //  | [39:20]      | mask    |
    //  | [55:40]      | stride  |
    addrCtl_t addr   = addrCtl & 0xfffff;
    addrCtl_t mask   = (addrCtl>>20) & 0xfffff;
    addrCtl_t stride = (addrCtl>>40) & 0xffff;
    // eew_nb
    //  | encoding | element size |
    //  | 3'b000   | 4-bit        |
    //  | 3'b001   | 8-bit        |
    //  | 3'b010   | 16-bit       |
    //  | 3'b011   | 32-bit       |
    //  | 3'b100   | 64-bit       |
    uint32_t eew_nb = ((func & 0xF) == 8) ? 0 : ((func & 0x3) + 1);
    // transfer size in bit: VL * (2^eew_nb) * 4
    uint32_t size = ((get_IS() & 0xffffffff) << eew_nb) * 4;
    // cmdInfo[1:0] =2 (load)
    // cmdInfo[21:2]=addr
    // cmdInfo[53:22]=transfer size
    // cmdInfo[59:54]=func
    // cmdInfo[61:60]=3 (VRF)
    cmdInfo |= (((cmdInfo_t)3)<<25);
cmdInfo |=(((cmdInfo_t)7)<<5);
cmdInfo |=(((cmdInfo_t)0)<<8);
cmdInfo |= (((cmdInfo_t)bc)<<12);
cmdInfo |=(((cmdInfo_t)0)<<13);
cmdInfo |=(((cmdInfo_t)0)<<19);
cmdInfo |=(((cmdInfo_t)4)<<20);
cmdInfo |=(((cmdInfo_t)0)<<23);
cmdInfo |=(((cmdInfo_t)0)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);
                                                
       acp_write_cmdValid(1);                                                         
       acp_write_cmdInfo(cmdInfo);                                                    
       while (!acp_read_cmdReady())                                                   
         ace_advance_cycle(1);                                                        
       ace_sc_wait(0.001);                                                            
       acp_sc_write_cmdValid(0);                                                      
       // post-update the address                                                     
       //   | mode | next address                      |                              
       //   | 0    | current address (unchanged)       |                              
       //   | 1    | current address + transfer size/8 |                              
       //   | 2    | current address + stride          |                              
       addrCtl = addrCtl & ~mask; //maintain original mask, stride and masked address 
  %};
};

insn tmv_tv_high {
  op = {in vrf data:5:7, io addrCtl addrCtl:1:26, func6 func:6:20, imm1 bc:1:19,  671117312:5:27|7:12};
  implied_op= {cmdInfo, cmdValid, cmdReady};
  csr_op= {pc, vl, vtype};
  streaming_port = store;
  csim= %{
    // addrCtl
    //  | bit position | content |
    //  | [19:0]       | address |
    //  | [39:20]      | mask    |
    //  | [55:40]      | stride  |
    addrCtl_t addr   = addrCtl & 0xfffff;
    addrCtl_t mask   = (addrCtl>>20) & 0xfffff;
    addrCtl_t stride = (addrCtl>>40) & 0xffff;
    // eew_nb
    //  | encoding | element size |
    //  | 3'b000   | 4-bit        |
    //  | 3'b001   | 8-bit        |
    //  | 3'b010   | 16-bit       |
    //  | 3'b011   | 32-bit       |
    //  | 3'b100   | 64-bit       |
    uint32_t eew_nb = ((func & 0xF) == 8) ? 0 : ((func & 0x3) + 1);
    // transfer size in bit: VL * (2^eew_nb) * 4
    uint32_t size = ((get_IS() & 0xffffffff) << eew_nb) * 4;
    // cmdInfo[1:0] =2 (load)
    // cmdInfo[21:2]=addr
    // cmdInfo[53:22]=transfer size
    // cmdInfo[59:54]=func
    // cmdInfo[61:60]=3 (VRF)
    cmdInfo |= (((cmdInfo_t)1)<<28);
cmdInfo |= (((cmdInfo_t)3)<<25);
cmdInfo |=(((cmdInfo_t)7)<<5);
cmdInfo |=(((cmdInfo_t)0)<<8);
cmdInfo |= (((cmdInfo_t)bc)<<12);
cmdInfo |=(((cmdInfo_t)0)<<13);
cmdInfo |=(((cmdInfo_t)0)<<19);
cmdInfo |=(((cmdInfo_t)5)<<20);
cmdInfo |=(((cmdInfo_t)0)<<23);
cmdInfo |=(((cmdInfo_t)0)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);
                                                
       acp_write_cmdValid(1);                                                         
       acp_write_cmdInfo(cmdInfo);                                                    
       while (!acp_read_cmdReady())                                                   
         ace_advance_cycle(1);                                                        
       ace_sc_wait(0.001);                                                            
       acp_sc_write_cmdValid(0);                                                      
       // post-update the address                                                     
       //   | mode | next address                      |                              
       //   | 0    | current address (unchanged)       |                              
       //   | 1    | current address + transfer size/8 |                              
       //   | 2    | current address + stride          |                              
       addrCtl = addrCtl & ~mask; //maintain original mask, stride and masked address 
  %};
};


insn tmv_rt_high {
  op = {out xrf data:5:7, io addrCtl addrCtl:1:26, func6 func:6:20, imm1 bc:1:19, 28672:5:27|7:12};

  implied_op= {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  streaming_port = load;
  csim= %{
    // addrCtl
    //  | bit position | content |
    //  | [19:0]       | address |
    //  | [39:20]      | mask    |
    //  | [55:40]      | stride  |
    addrCtl_t addr   = addrCtl & 0xfffff;
    addrCtl_t mask   = (addrCtl>>20) & 0xfffff;
    addrCtl_t stride = (addrCtl>>40) & 0xffff;

    // eew_nb
    //  | encoding | element size |
    //  | 3'b000   | 4-bit        |
    //  | 3'b001   | 8-bit        |
    //  | 3'b010   | 16-bit       |
    //  | 3'b011   | 32-bit       |
    //  | 3'b100   | 64-bit       |
    uint32_t eew_nb = ((func & 0xF) == 8) ? 0 : ((func & 0x3) + 1);

    // transfer size in bit: (2^eew_nb) * 4
    uint32_t size = (1 << eew_nb) * 4;

    // cmdInfo[1:0] =2 (load)
    // cmdInfo[21:2]=addr
    // cmdInfo[53:22]=transfer size
    // cmdInfo[59:54]=func
    // cmdInfo[61:60]=1 (XRF)
    cmdInfo |= (((cmdInfo_t)1)<<28);
cmdInfo |= (((cmdInfo_t)3)<<25);
cmdInfo |=(((cmdInfo_t)7)<<5);
cmdInfo |=(((cmdInfo_t)0)<<8);
cmdInfo |= (((cmdInfo_t)bc)<<12);
cmdInfo |=(((cmdInfo_t)32)<<13);
cmdInfo |=(((cmdInfo_t)0)<<19);
cmdInfo |=(((cmdInfo_t)0)<<20);
cmdInfo |=(((cmdInfo_t)0)<<23);
cmdInfo |=(((cmdInfo_t)0)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);
                                                

                                                                                      
       acp_write_cmdValid(1);                                                         
       acp_write_cmdInfo(cmdInfo);                                                    
       while (!acp_read_cmdReady())                                                   
         ace_advance_cycle(1);                                                        
       ace_sc_wait(0.001);                                                            
       acp_sc_write_cmdValid(0);                                                      
                                                                                      
       // post-update the address                                                     
       //   | mode | next address                      |                              
       //   | 0    | current address (unchanged)       |                              
       //   | 1    | current address + transfer size/8 |                              
       //   | 2    | current address + stride          |                              
       addrCtl = addrCtl & ~mask; //maintain original mask, stride and masked address 
  %};
};

insn tmv_tr_high {
  op = {in xrf data:5:7, io addrCtl addrCtl:1:26, func6 func:6:20, imm1 bc:1:19, 134246400:5:27|7:12};
  implied_op= {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  streaming_port = store;
  csim= %{
    // addrCtl
    //  | bit position | content |
    //  | [19:0]       | address |
    //  | [39:20]      | mask    |
    //  | [55:40]      | stride  |
    addrCtl_t addr   = addrCtl & 0xfffff;
    addrCtl_t mask   = (addrCtl>>20) & 0xfffff;
    addrCtl_t stride = (addrCtl>>40) & 0xffff;

    // eew_nb
    //  | encoding | element size |
    //  | 3'b000   | 4-bit        |
    //  | 3'b001   | 8-bit        |
    //  | 3'b010   | 16-bit       |
    //  | 3'b011   | 32-bit       |
    //  | 3'b100   | 64-bit       |
    uint32_t eew_nb = ((func & 0xF) == 8) ? 0 : ((func & 0x3) + 1);

    // transfer size in bit: (2^eew_nb) * 4
    uint32_t size = (1 << eew_nb) * 4;

    // cmdInfo[1:0] =3 (store)
    // cmdInfo[21:2]=addr
    // cmdInfo[53:22]=transfer size
    // cmdInfo[59:54]=func
    // cmdInfo[61:60]=1 (XRF)
    cmdInfo |= (((cmdInfo_t)3)<<25);
cmdInfo |=(((cmdInfo_t)7)<<5);
cmdInfo |=(((cmdInfo_t)0)<<8);
cmdInfo |= (((cmdInfo_t)bc)<<12);
cmdInfo |=(((cmdInfo_t)32)<<13);
cmdInfo |=(((cmdInfo_t)0)<<19);
cmdInfo |=(((cmdInfo_t)1)<<20);
cmdInfo |=(((cmdInfo_t)0)<<23);
cmdInfo |=(((cmdInfo_t)0)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);
                                                
                                                                                      
       acp_write_cmdValid(1);                                                         
       acp_write_cmdInfo(cmdInfo);                                                    
       while (!acp_read_cmdReady())                                                   
         ace_advance_cycle(1);                                                        
       ace_sc_wait(0.001);                                                            
       acp_sc_write_cmdValid(0);                                                      
                                                                                      
       // post-update the address                                                     
       //   | mode | next address                      |                              
       //   | 0    | current address (unchanged)       |                              
       //   | 1    | current address + transfer size/8 |                              
       //   | 2    | current address + stride          |                              
       addrCtl = addrCtl & ~mask; //maintain original mask, stride and masked address 
    
  %};

};

insn tmv_ti_high {
  op = {in xrf data:5:7, imm6 func:12:15, 939552768:5:27|3:12};
  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |= (((cmdInfo_t)3)<<25);
cmdInfo |=(((cmdInfo_t)7)<<5);
cmdInfo |=(((cmdInfo_t)0)<<8);
cmdInfo |= (((cmdInfo_t)bc)<<12);
cmdInfo |= (((cmdInfo_t)imm_1));
cmdInfo |=(((cmdInfo_t)0)<<15);
cmdInfo |=(((cmdInfo_t)0)<<19);
cmdInfo |=(((cmdInfo_t)7)<<20);
cmdInfo |=(((cmdInfo_t)0)<<23);
cmdInfo |=(((cmdInfo_t)0)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);

  %};
};

insn tmv_tt_high {
  op = {in xrf rs3:5:7, imm8 Ts1:8:15, imm3 size:3:23, 268464251:6:26|7:12};
  implied_op = {cmdInfo, cmdValid, cmdReady};
  //csr_op= {pc, vl, vtype};
  csim = %{
cmdInfo |= (((cmdInfo_t)3)<<25);
cmdInfo |=(((cmdInfo_t)7)<<5);
cmdInfo |=(((cmdInfo_t)0)<<8);
cmdInfo |= (((cmdInfo_t)bc)<<12);
cmdInfo |= (((cmdInfo_t)imm_1));
cmdInfo |=(((cmdInfo_t)0)<<15);
cmdInfo |=(((cmdInfo_t)0)<<19);
cmdInfo |=(((cmdInfo_t)7)<<20);
cmdInfo |=(((cmdInfo_t)0)<<23);
cmdInfo |=(((cmdInfo_t)0)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

    acp_write_cmdValid(1);
    acp_write_cmdInfo(cmdInfo);
    while (!acp_read_cmdReady()) {
      ace_advance_cycle(1);
    }
    ace_sc_wait(0.001);
    acp_sc_write_cmdValid(0);

  %};
};

insn asp_vec_in {
  op = {in vrf data:5:7, io addrCtl addrCtl:1:26, func6 func:6:20, imm1 bc:1:19,  4026560512:5:27|7:12};
  implied_op= {cmdInfo, cmdValid, cmdReady};
  csr_op= {pc, vl, vtype};
  streaming_port = store;
  csim= %{
    // addrCtl
    //  | bit position | content |
    //  | [19:0]       | address |
    //  | [39:20]      | mask    |
    //  | [55:40]      | stride  |
    addrCtl_t addr   = addrCtl & 0xfffff;
    addrCtl_t mask   = (addrCtl>>20) & 0xfffff;
    addrCtl_t stride = (addrCtl>>40) & 0xffff;
    // eew_nb
    //  | encoding | element size |
    //  | 3'b000   | 4-bit        |
    //  | 3'b001   | 8-bit        |
    //  | 3'b010   | 16-bit       |
    //  | 3'b011   | 32-bit       |
    //  | 3'b100   | 64-bit       |
    uint32_t eew_nb = ((func & 0xF) == 8) ? 0 : ((func & 0x3) + 1);
    // transfer size in bit: VL * (2^eew_nb) * 4
    uint32_t size = ((get_IS() & 0xffffffff) << eew_nb) * 4;
    // cmdInfo[1:0] =3 (store)
    // cmdInfo[21:2]=addr
    // cmdInfo[53:22]=transfer size
    // cmdInfo[59:54]=func
    // cmdInfo[61:60]=3 (vRF)
    cmdInfo |= (((cmdInfo_t)1)<<28);
cmdInfo |= (((cmdInfo_t)3)<<25);
cmdInfo |=(((cmdInfo_t)7)<<5);
cmdInfo |=(((cmdInfo_t)0)<<8);
cmdInfo |=(((cmdInfo_t)0)<<13);
cmdInfo |=(((cmdInfo_t)7)<<21);
cmdInfo |=(((cmdInfo_t)1)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);
                                                
       acp_write_cmdValid(1);                                                         
       acp_write_cmdInfo(cmdInfo);                                                    
       while (!acp_read_cmdReady())                                                   
         ace_advance_cycle(1);                                                        
       ace_sc_wait(0.001);                                                            
       acp_sc_write_cmdValid(0);                                                      
       // post-update the address                                                     
       //   | mode | next address                      |                              
       //   | 0    | current address (unchanged)       |                              
       //   | 1    | current address + transfer size/8 |                              
       //   | 2    | current address + stride          |                              
       addrCtl = addrCtl & ~mask; //maintain original mask, stride and masked address 
  %};
};

insn tsync_i {
  op = {imm5 sync_id:5:15, imm1 blk:1:30, imm2 scope:2:28, 20480:1:31|3:25|5:20|3:12|5:7};
  implied_op = {cmdInfo, cmdValid, cmdReady,tsync_vld,tsync_info,tsync_release,tsync_error};
  //csr_op= {pc, vl, vtype};
  csim = %{
    cmdInfo = ((0x7b << 64) | (2147504128 << 64) );
    tsync_info |= ((tsync_info_t)scope);
    tsync_info |= (((tsync_info_t)(!blk))<<1);
    tsync_info |= (((tsync_info_t)sync_id)<<2);
    acp_write_tsync_vld(1);
    acp_write_tsync_info(tsync_info);
    ace_advance_cycle(1);
    ace_sc_wait(0.001);
    acp_write_tsync_vld(0);

    if(blk){
      while(!acp_read_tsync_release()){
        ace_advance_cycle(1);  
      }
    }

  %};
};

u_insn ace_bsync {
	op = {in XRF sync_id:5:15, 2147512320:12:20|8:7};
	csim = %{
cmdInfo |=(((cmdInfo_t)0)<<0);
cmdInfo |= (((cmdInfo_t)3)<<25);
cmdInfo |=(((cmdInfo_t)7)<<5);
cmdInfo |= (((cmdInfo_t)sync_id)<<8);
cmdInfo |=(((cmdInfo_t)0)<<13);
cmdInfo |=(((cmdInfo_t)0)<<21);
cmdInfo |=(((cmdInfo_t)1)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

	%};
	utility_kind = bsync;
};

u_insn ace_nbsync {
	op = {in XRF sync_id:5:15, 2684383232:12:20|8:7};
	csim = %{
cmdInfo |=(((cmdInfo_t)0)<<0);
cmdInfo |= (((cmdInfo_t)3)<<25);
cmdInfo |=(((cmdInfo_t)7)<<5);
cmdInfo |= (((cmdInfo_t)sync_id)<<8);
cmdInfo |=(((cmdInfo_t)0)<<13);
cmdInfo |=(((cmdInfo_t)2)<<21);
cmdInfo |=(((cmdInfo_t)1)<<24);
cmdInfo |= (((cmdInfo_t)get_IS())<<160);
//cmdInfo |= (((cmdInfo_t)csr_vtype)<<171);
cmdInfo |= (((cmdInfo_t)(uint32_t(this->insn)))<<177);

	%};
	utility_kind = nbsync;
};
