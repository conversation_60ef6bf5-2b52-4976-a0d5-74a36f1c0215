// Simple test for SystemVerilog decoder without using packages
// Testing instruction 0x8003907b8200647b

`timescale 1ns/1ps

module test_systemverilog_simple;

    // Test parameters
    parameter [31:0] WORD1 = 32'h8200647b;  // Low 32 bits
    parameter [31:0] WORD2 = 32'h8003907b;  // High 32 bits
    parameter [63:0] FULL_INSTRUCTION = {WORD2, WORD1};
    
    // Simulate the SystemVerilog field extraction logic
    reg [7:0] td;
    reg [4:0] rs1, rs2;
    reg [127:0] instruction_data;

    // Instruction identification fields
    reg [6:0] ace_op;
    reg [2:0] tuop;
    reg [5:0] memuop;
    reg [1:0] lsuop;
    reg [2:0] second_tuop;
    
    initial begin
        $display("=== SystemVerilog Field Extraction Test ===");
        $display("");
        
        $display("Test Data:");
        $display("  Full instruction: 0x%016x", FULL_INSTRUCTION);
        $display("  Word 1 (low):     0x%08x", WORD1);
        $display("  Word 2 (high):    0x%08x", WORD2);
        $display("");
        
        // Simulate instruction_data as used in SystemVerilog
        instruction_data = {64'h0, FULL_INSTRUCTION};
        $display("  instruction_data: 0x%032x", instruction_data);
        $display("");
        
        // Test the FIXED field extraction (as in the corrected SystemVerilog code)
        $display("=== Testing FIXED Field Extraction ===");
        $display("Using corrected SystemVerilog field positions:");
        
        // This simulates the fixed SystemVerilog code:
        // td = instruction_data[32+30:32+23];  // Td field in second word bits [30:23]
        // rs1 = instruction_data[32+19:32+15]; // rs1 field in second word bits [19:15]
        // rs2 = instruction_data[24:20];       // rs3 field in first word bits [24:20]
        
        td = instruction_data[32+30:32+23];   // bits [62:55]
        rs1 = instruction_data[32+19:32+15];  // bits [51:47]
        rs2 = instruction_data[24:20];        // bits [24:20]
        
        $display("  td = instruction_data[32+30:32+23] = instruction_data[62:55] = %0d", td);
        $display("  rs1 = instruction_data[32+19:32+15] = instruction_data[51:47] = %0d", rs1);
        $display("  rs2 = instruction_data[24:20] = %0d", rs2);
        $display("");
        
        // Format operands as in SystemVerilog
        $display("  Formatted operands: t%0d, (x%0d), x%0d", td, rs1, rs2);
        $display("  Complete instruction: tld.trr.blk.mx48.share t%0d, (x%0d), x%0d", td, rs1, rs2);
        $display("");
        
        // Verify against expected result
        $display("=== Verification ===");
        $display("  Expected: tld.trr.blk.mx48.share t0, (x7), x0");
        
        if (td == 0 && rs1 == 7 && rs2 == 0) begin
            $display("  ✓ SUCCESS: SystemVerilog field extraction is correct!");
            $display("  ✓ The fix is working properly");
        end else begin
            $display("  ✗ FAILURE: SystemVerilog field extraction is incorrect");
            $display("  Expected: td=0, rs1=7, rs2=0");
            $display("  Actual:   td=%0d, rs1=%0d, rs2=%0d", td, rs1, rs2);
        end
        $display("");
        
        // Test instruction identification logic
        $display("=== Instruction Identification ===");

        ace_op = instruction_data[6:0];
        tuop = instruction_data[14:12];
        memuop = instruction_data[30:25];
        lsuop = instruction_data[11:10];
        second_tuop = instruction_data[32+14:32+12];
        
        $display("  ace_op: 0x%02x (%s)", ace_op, ace_op == 7'h7b ? "TILE" : "NOT_TILE");
        $display("  tuop: %0d (%s)", tuop, tuop == 3'b110 ? "110" : "OTHER");
        $display("  memuop: %0d (%s)", memuop, memuop == 6'b000001 ? "000001" : "OTHER");
        $display("  lsuop: %0d (%s)", lsuop, lsuop == 2'b01 ? "01" : "OTHER");
        $display("  second_tuop: %0d (%s)", second_tuop, second_tuop == 3'b001 ? "001" : "OTHER");
        
        // Check if this matches tld.trr.blk.mx48.share pattern
        if (ace_op == 7'h7b) begin
            if (tuop == 3'b110) begin
                if (memuop == 6'b000001) begin
                    if (lsuop == 2'b01) begin
                        if (second_tuop == 3'b001) begin
                            $display("  ✓ Instruction correctly identified as tld.trr.blk.mx48.share");
                        end else begin
                            $display("  ✗ second_tuop check failed");
                        end
                    end else begin
                        $display("  ✗ lsuop check failed");
                    end
                end else begin
                    $display("  ✗ memuop check failed");
                end
            end else begin
                $display("  ✗ tuop check failed");
            end
        end else begin
            $display("  ✗ ace_op check failed");
        end
        $display("");
        
        $display("=== Test Summary ===");
        if (td == 0 && rs1 == 7 && rs2 == 0) begin
            $display("✓ OVERALL SUCCESS: The SystemVerilog fix is working correctly!");
            $display("  Instruction 0x8003907b8200647b correctly decodes to:");
            $display("  tld.trr.blk.mx48.share t0, (x7), x0");
        end else begin
            $display("✗ OVERALL FAILURE: The SystemVerilog fix needs more work");
        end
        
        $finish;
    end

endmodule
