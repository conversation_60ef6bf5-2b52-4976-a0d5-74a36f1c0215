// Fixed test for twait instruction
module fixed_test;

    // Simplified version of extract_instruction_name that only handles tuop_101
    function automatic string extract_sync_instruction_name(
        input logic [127:0] instruction_data
    );
        logic [6:0] ace_op;
        logic [2:0] tuop;
        logic [2:0] ctrluop;
        logic [2:0] waitop;
        logic isMem;

        ace_op = instruction_data[6:0];

        if (ace_op != 7'b1111011) begin
            return "unknown_non_tile";
        end

        tuop = instruction_data[14:12];

        if (tuop == 3'b101) begin // Sync operations (tuop_101)
            ctrluop = instruction_data[25:23];
            waitop = instruction_data[30:28];
            
            if (ctrluop == 3'b001) begin // twait operations
                if (waitop == 3'b111) begin // twait (basic)
                    isMem = instruction_data[26];
                    if (isMem)
                        return "twait.mem";
                    else
                        return "twait";
                end else begin
                    return "unknown_waitop";
                end
            end else begin
                return "unknown_ctrluop";
            end
        end else begin
            return "unknown_tuop";
        end
    endfunction

    initial begin
        logic [31:0] test_instruction;
        logic [127:0] instruction_data;
        string result;

        $display("=== Fixed twait test ===");
        
        // Test the specific instruction: 0x7080507B
        test_instruction = 32'h7080507B;
        instruction_data = {96'h0, test_instruction};
        
        $display("Testing instruction: 0x%08x", test_instruction);
        
        result = extract_sync_instruction_name(instruction_data);
        $display("Result: %s", result);
        
        if (result == "twait") begin
            $display("✓ SUCCESS: Instruction 0x7080507B correctly identified as 'twait'");
        end else begin
            $display("✗ FAILURE: Expected 'twait', got '%s'", result);
        end
        
        $display("=== Test Complete ===");
    end

endmodule
