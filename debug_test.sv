// Debug test for twait instruction

module debug_test;

    initial begin
        logic [31:0] test_instruction;
        logic [6:0] ace_op;
        logic [2:0] tuop;
        logic [2:0] ctrluop;
        logic [2:0] waitop;
        logic isMem;

        $display("=== Debug twait test ===");
        
        // Test the specific instruction: 0x7080507B
        test_instruction = 32'h7080507B;
        $display("Testing instruction: 0x%08x", test_instruction);
        
        // Extract fields manually
        ace_op = test_instruction[6:0];
        tuop = test_instruction[14:12];
        ctrluop = test_instruction[25:23];
        waitop = test_instruction[30:28];
        isMem = test_instruction[26];
        
        $display("ace_op: %b (%d)", ace_op, ace_op);
        $display("tuop: %b (%d)", tuop, tuop);
        $display("ctrluop: %b (%d)", ctrluop, ctrluop);
        $display("waitop: %b (%d)", waitop, waitop);
        $display("isMem: %b (%d)", isMem, isMem);
        
        // Check conditions step by step
        if (ace_op == 7'b1111011) begin
            $display("✓ ACE_OP matches tile instruction");
            
            if (tuop == 3'b101) begin
                $display("✓ tuop matches sync operations (101)");
                
                if (ctrluop == 3'b001) begin
                    $display("✓ ctrluop matches twait operations (001)");
                    
                    if (waitop == 3'b111) begin
                        $display("✓ waitop matches basic twait (111)");
                        $display("✓ This should be identified as 'twait' or 'twait.mem'");
                        
                        if (isMem == 1'b0) begin
                            $display("✓ isMem=0, should be 'twait'");
                        end else begin
                            $display("✓ isMem=1, should be 'twait.mem'");
                        end
                    end else begin
                        $display("✗ waitop does not match basic twait");
                    end
                end else begin
                    $display("✗ ctrluop does not match twait operations");
                end
            end else begin
                $display("✗ tuop does not match sync operations");
            end
        end else begin
            $display("✗ ACE_OP does not match tile instruction");
        end
        
        $display("=== Debug Complete ===");
    end

endmodule
