// Debug script for instruction 0x8003907b8200647b
// This should decode as: tld.trr.mx48.share T0, (t2), zero

`timescale 1ns/1ps

import tile_decoder_pkg::*;

module debug_instruction;

    initial begin
        logic [31:0] word1, word2;
        logic [63:0] full_instruction;
        instr_collector_t collector;
        string result;
        instr_length_e expected_length;
        logic is_tile;
        
        $display("=== Debugging Instruction 0x8003907b8200647b ===");
        $display("");
        
        // 测试指令 0x8003907b8200647b
        word1 = 32'h8200647b;  // 低32位
        word2 = 32'h8003907b;  // 高32位
        full_instruction = {word2, word1};
        
        $display("Full instruction: 0x%016x", full_instruction);
        $display("Word 1 (low):     0x%08x", word1);
        $display("Word 2 (high):    0x%08x", word2);
        $display("");
        
        // 分析第一个字的字段
        $display("Word 1 analysis:");
        $display("  ACE_OP [6:0]:   0x%02x (%s)", word1[6:0], 
                 word1[6:0] == 7'b1111011 ? "TILE" : "NOT_TILE");
        $display("  lsuop [11:10]:  %0d", word1[11:10]);
        $display("  tuop [14:12]:   %0d", word1[14:12]);
        $display("  rs3 [24:20]:    %0d", word1[24:20]);
        $display("  memuop [30:25]: %0d (0b%06b)", word1[30:25], word1[30:25]);
        $display("");
        
        // 分析第二个字的字段
        $display("Word 2 analysis:");
        $display("  ACE_OP [6:0]:   0x%02x", word2[6:0]);
        $display("  tuop [14:12]:   %0d", word2[14:12]);
        $display("  rs1 [19:15]:    %0d", word2[19:15]);
        $display("  Td [30:23]:     %0d", word2[30:23]);
        $display("");
        
        // 测试 is_tile_instruction
        is_tile = tile_decoder_pkg::is_tile_instruction(word1);
        $display("is_tile_instruction(word1): %s", is_tile ? "TRUE" : "FALSE");
        
        // 测试 get_instruction_length
        expected_length = tile_decoder_pkg::get_instruction_length(word1);
        $display("get_instruction_length(word1): %s", expected_length.name());
        $display("");
        
        // 初始化收集器
        collector = tile_decoder_pkg::init_collector(word1);
        $display("Collector after init:");
        $display("  collected_words: %0d", collector.collected_words);
        $display("  expected_length: %s", collector.expected_length.name());
        $display("  is_complete: %s", collector.is_complete ? "TRUE" : "FALSE");
        $display("  is_tile_instr: %s", collector.is_tile_instr ? "TRUE" : "FALSE");
        $display("");
        
        // 添加第二个字
        if (!collector.is_complete) begin
            collector = tile_decoder_pkg::add_word_to_collector(collector, word2);
            $display("Collector after adding word2:");
            $display("  collected_words: %0d", collector.collected_words);
            $display("  is_complete: %s", collector.is_complete ? "TRUE" : "FALSE");
            $display("");
        end
        
        // 反编译
        if (collector.is_complete) begin
            result = tile_decoder_pkg::disassemble_instruction(
                collector.instruction_data, collector.expected_length);
            $display("Disassembly result: %s", result);
            $display("");
            
            // 详细分析反编译过程
            $display("Detailed disassembly analysis:");
            $display("  instruction_data: 0x%032x", collector.instruction_data);
            $display("  length: %s", collector.expected_length.name());
            
            // 手动检查字段
            logic [6:0] ace_op = collector.instruction_data[6:0];
            logic [2:0] tuop = collector.instruction_data[14:12];
            logic [5:0] memuop = collector.instruction_data[30:25];
            logic [1:0] lsuop = collector.instruction_data[11:10];
            logic [2:0] second_tuop = collector.instruction_data[32+14:32+12];
            
            $display("  ace_op: 0x%02x", ace_op);
            $display("  tuop: %0d", tuop);
            $display("  memuop: %0d (0b%06b)", memuop, memuop);
            $display("  lsuop: %0d", lsuop);
            $display("  second_tuop: %0d", second_tuop);
            $display("");
            
            // 检查条件
            $display("Condition checks:");
            $display("  ace_op == TILE_ACE_OP: %s", ace_op == 7'b1111011 ? "TRUE" : "FALSE");
            $display("  tuop == 3'b110: %s", tuop == 3'b110 ? "TRUE" : "FALSE");
            $display("  length == INSTR_64BIT: %s", collector.expected_length == INSTR_64BIT ? "TRUE" : "FALSE");
            $display("  memuop == 6'b000001: %s", memuop == 6'b000001 ? "TRUE" : "FALSE");
            $display("  second_tuop == 3'b001: %s", second_tuop == 3'b001 ? "TRUE" : "FALSE");
            $display("  lsuop == 2'b01: %s", lsuop == 2'b01 ? "TRUE" : "FALSE");
            $display("");
            
            // 预期结果
            $display("Expected result: tld.trr.mx48.share T0, (t2), zero");
            if (result == "tld.trr.blk.mx48.share") begin
                $display("SUCCESS: Instruction name matches expected pattern!");
            end else begin
                $display("ERROR: Instruction name does not match!");
            end
        end else begin
            $display("ERROR: Instruction collection not complete!");
        end
        
        $display("\n=== Debug Complete ===");
    end

endmodule
