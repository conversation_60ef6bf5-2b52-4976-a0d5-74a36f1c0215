// Test the actual SystemVerilog package functions
// This tests the real tile_decoder_pkg functions

`timescale 1ns/1ps

// Import the package
import tile_decoder_pkg::*;

module test_package_functions;

    initial begin
        logic [31:0] word1, word2;
        instr_collector_t collector;
        string result;
        instr_length_e expected_length;
        
        $display("=== Testing Actual SystemVerilog Package Functions ===");
        $display("");
        
        // Test instruction 0x8003907b8200647b
        word1 = 32'h8200647b;  // Low 32 bits
        word2 = 32'h8003907b;  // High 32 bits
        
        $display("Test Data:");
        $display("  Word 1 (low):  0x%08x", word1);
        $display("  Word 2 (high): 0x%08x", word2);
        $display("");
        
        // Step 1: Test is_tile_instruction
        $display("Step 1: Testing is_tile_instruction()");
        if (is_tile_instruction(word1)) begin
            $display("  ✓ Correctly identified as tile instruction");
        end else begin
            $display("  ✗ Failed to identify as tile instruction");
        end
        $display("");
        
        // Step 2: Test get_instruction_length
        $display("Step 2: Testing get_instruction_length()");
        expected_length = get_instruction_length(word1);
        $display("  Expected length: %s", expected_length.name());
        if (expected_length == INSTR_64BIT) begin
            $display("  ✓ Correctly identified as 64-bit instruction");
        end else begin
            $display("  ✗ Incorrect length identification");
        end
        $display("");
        
        // Step 3: Test init_collector
        $display("Step 3: Testing init_collector()");
        collector = init_collector(word1);
        $display("  Is tile: %s", collector.is_tile_instr ? "YES" : "NO");
        $display("  Expected length: %s", collector.expected_length.name());
        $display("  Complete: %s", collector.is_complete ? "YES" : "NO");
        $display("  Collected words: %0d", collector.collected_words);
        $display("");
        
        // Step 4: Test add_word_to_collector
        if (!collector.is_complete) begin
            $display("Step 4: Testing add_word_to_collector()");
            collector = add_word_to_collector(collector, word2);
            $display("  Complete after adding word2: %s", collector.is_complete ? "YES" : "NO");
            $display("  Collected words: %0d", collector.collected_words);
            $display("  instruction_data: 0x%032x", collector.instruction_data);
            $display("");
        end
        
        // Step 5: Test extract_instruction_name
        if (collector.is_complete) begin
            $display("Step 5: Testing extract_instruction_name()");
            string instr_name = extract_instruction_name(collector.instruction_data, collector.expected_length);
            $display("  Instruction name: %s", instr_name);
            
            if (instr_name == "tld.trr.blk.mx48.share") begin
                $display("  ✓ Correct instruction name");
            end else begin
                $display("  ✗ Incorrect instruction name");
                $display("  Expected: tld.trr.blk.mx48.share");
            end
            $display("");
            
            // Step 6: Test format_operands
            $display("Step 6: Testing format_operands()");
            string operands = format_operands(collector.instruction_data, collector.expected_length, instr_name);
            $display("  Operands: %s", operands);
            
            if (operands == "t0, (x7), x0") begin
                $display("  ✓ Correct operands");
            end else begin
                $display("  ✗ Incorrect operands");
                $display("  Expected: t0, (x7), x0");
            end
            $display("");
            
            // Step 7: Test disassemble_instruction
            $display("Step 7: Testing disassemble_instruction()");
            result = disassemble_instruction(collector.instruction_data, collector.expected_length);
            $display("  Full result: %s", result);
            
            if (result == "tld.trr.blk.mx48.share t0, (x7), x0") begin
                $display("  ✓ Perfect match with expected result!");
            end else begin
                $display("  ✗ Result does not match expected");
                $display("  Expected: tld.trr.blk.mx48.share t0, (x7), x0");
            end
            $display("");
        end
        
        $display("=== Final Summary ===");
        if (collector.is_complete && result == "tld.trr.blk.mx48.share t0, (x7), x0") begin
            $display("✓ ALL TESTS PASSED!");
            $display("The SystemVerilog package functions are working correctly.");
            $display("Instruction 0x8003907b8200647b correctly decodes to:");
            $display("  %s", result);
        end else begin
            $display("✗ SOME TESTS FAILED!");
            $display("The SystemVerilog package needs debugging.");
        end
        
        $finish;
    end

endmodule
