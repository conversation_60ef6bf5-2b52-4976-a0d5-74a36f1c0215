// Debug why field values are showing as 'x' in VCS
// Testing instruction 0x8003907b8200647b

`timescale 1ns/1ps

import tile_decoder_pkg::*;

module debug_x_values;

    initial begin
        logic [31:0] word1, word2;
        instr_collector_t collector;
        
        $display("=== Debug X Values for 0x8003907b8200647b ===");
        $display("");
        
        // Test instruction 0x8003907b8200647b
        word1 = 32'h8200647b;  // Low 32 bits
        word2 = 32'h8003907b;  // High 32 bits
        
        $display("Input Data:");
        $display("  Word 1 (low):  0x%08x = %032b", word1, word1);
        $display("  Word 2 (high): 0x%08x = %032b", word2, word2);
        $display("");
        
        // Step 1: Check individual word field extraction
        $display("=== Step 1: Direct Word Field Extraction ===");
        
        // Extract fields directly from words
        logic [6:0] ace_op1 = word1[6:0];
        logic [2:0] tuop1 = word1[14:12];
        logic [5:0] memuop1 = word1[30:25];
        logic [1:0] lsuop1 = word1[11:10];
        
        logic [6:0] ace_op2 = word2[6:0];
        logic [2:0] tuop2 = word2[14:12];
        logic [4:0] rs1_direct = word2[19:15];
        logic [7:0] td_direct = word2[30:23];
        
        $display("  From Word1:");
        $display("    ace_op1 [6:0]:   0x%02x = %0d", ace_op1, ace_op1);
        $display("    tuop1 [14:12]:   %0d", tuop1);
        $display("    memuop1 [30:25]: %0d", memuop1);
        $display("    lsuop1 [11:10]:  %0d", lsuop1);
        $display("");
        
        $display("  From Word2:");
        $display("    ace_op2 [6:0]:   0x%02x = %0d", ace_op2, ace_op2);
        $display("    tuop2 [14:12]:   %0d", tuop2);
        $display("    rs1 [19:15]:     %0d", rs1_direct);
        $display("    td [30:23]:      %0d", td_direct);
        $display("");
        
        // Step 2: Initialize collector and check instruction_data
        $display("=== Step 2: Collector Initialization ===");
        collector = tile_decoder_pkg::init_collector(word1);
        
        $display("  After init_collector:");
        $display("    instruction_data: 0x%032x", collector.instruction_data);
        $display("    collected_words: %0d", collector.collected_words);
        $display("    is_complete: %s", collector.is_complete ? "YES" : "NO");
        $display("");
        
        // Check if first word is correctly stored
        logic [31:0] stored_word1 = collector.instruction_data[31:0];
        $display("    Stored word1: 0x%08x (should be 0x%08x)", stored_word1, word1);
        if (stored_word1 === word1) begin
            $display("    ✓ Word1 correctly stored");
        end else begin
            $display("    ✗ Word1 storage problem!");
        end
        $display("");
        
        // Step 3: Add second word
        if (!collector.is_complete) begin
            $display("=== Step 3: Adding Second Word ===");
            collector = tile_decoder_pkg::add_word_to_collector(collector, word2);
            
            $display("  After add_word_to_collector:");
            $display("    instruction_data: 0x%032x", collector.instruction_data);
            $display("    collected_words: %0d", collector.collected_words);
            $display("    is_complete: %s", collector.is_complete ? "YES" : "NO");
            $display("");
            
            // Check if both words are correctly stored
            logic [31:0] stored_word1_final = collector.instruction_data[31:0];
            logic [31:0] stored_word2_final = collector.instruction_data[63:32];
            
            $display("    Final stored word1: 0x%08x (should be 0x%08x)", stored_word1_final, word1);
            $display("    Final stored word2: 0x%08x (should be 0x%08x)", stored_word2_final, word2);
            
            if (stored_word1_final === word1 && stored_word2_final === word2) begin
                $display("    ✓ Both words correctly stored");
            end else begin
                $display("    ✗ Word storage problem!");
            end
            $display("");
        end
        
        // Step 4: Test field extraction from instruction_data
        if (collector.is_complete) begin
            $display("=== Step 4: Field Extraction from instruction_data ===");
            
            // Test the exact same field extractions that were showing 'x'
            logic [2:0] tuop_first_test = collector.instruction_data[14:12];
            logic [5:0] memuop_test = collector.instruction_data[30:25];
            logic [2:0] tuop_second_test = collector.instruction_data[32+14:32+12];
            
            $display("  Field extraction test:");
            $display("    tuop_first [14:12]:    %0d (from instruction_data[14:12])", tuop_first_test);
            $display("    memuop [30:25]:        %0d (from instruction_data[30:25])", memuop_test);
            $display("    tuop_second [46:44]:   %0d (from instruction_data[46:44])", tuop_second_test);
            $display("");
            
            // Compare with direct extraction
            $display("  Comparison with direct extraction:");
            $display("    tuop_first: instruction_data=%0d, direct=%0d, match=%s", 
                     tuop_first_test, tuop1, (tuop_first_test === tuop1) ? "YES" : "NO");
            $display("    memuop: instruction_data=%0d, direct=%0d, match=%s", 
                     memuop_test, memuop1, (memuop_test === memuop1) ? "YES" : "NO");
            $display("    tuop_second: instruction_data=%0d, direct=%0d, match=%s", 
                     tuop_second_test, tuop2, (tuop_second_test === tuop2) ? "YES" : "NO");
            $display("");
            
            // Test the corrected field positions
            $display("=== Step 5: Test Corrected Field Positions ===");
            logic [7:0] td_corrected = collector.instruction_data[32+30:32+23];
            logic [4:0] rs1_corrected = collector.instruction_data[32+19:32+15];
            logic [4:0] rs2_corrected = collector.instruction_data[24:20];
            
            $display("  Corrected field extraction:");
            $display("    td [62:55]:   %0d (should be 0)", td_corrected);
            $display("    rs1 [51:47]:  %0d (should be 7)", rs1_corrected);
            $display("    rs2 [24:20]:  %0d (should be 0)", rs2_corrected);
            $display("");
            
            // Compare with direct extraction
            logic [4:0] rs2_direct = word1[24:20];
            $display("  Comparison with direct extraction:");
            $display("    td: instruction_data=%0d, direct=%0d, match=%s", 
                     td_corrected, td_direct, (td_corrected === td_direct) ? "YES" : "NO");
            $display("    rs1: instruction_data=%0d, direct=%0d, match=%s", 
                     rs1_corrected, rs1_direct, (rs1_corrected === rs1_direct) ? "YES" : "NO");
            $display("    rs2: instruction_data=%0d, direct=%0d, match=%s", 
                     rs2_corrected, rs2_direct, (rs2_corrected === rs2_direct) ? "YES" : "NO");
            
        end else begin
            $display("ERROR: Instruction collection not complete!");
        end
        
        $display("");
        $display("=== Debug Complete ===");
        $finish;
    end

endmodule
