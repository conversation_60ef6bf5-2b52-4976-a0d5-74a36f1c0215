#!/usr/bin/env python3
"""
Test the corrected field extraction for instruction 0x8003907b8200647b
"""

def test_extraction():
    instruction = 0x8003907b8200647b
    
    print(f"测试指令: 0x{instruction:016x}")
    print()
    
    # 模拟修复后的SystemVerilog字段提取
    # td = instruction_data[62:55];        // Td field bits [62:55]
    # rs1 = instruction_data[51:47];       // rs1 field bits [51:47]
    # rs2 = instruction_data[24:20];       // rs3 field bits [24:20]
    
    td = (instruction >> 55) & 0xFF      # bits [62:55]
    rs1 = (instruction >> 47) & 0x1F     # bits [51:47]
    rs2 = (instruction >> 20) & 0x1F     # bits [24:20]
    
    print("修复后的字段提取:")
    print(f"  td (bits [62:55]):  {td}")
    print(f"  rs1 (bits [51:47]): {rs1}")
    print(f"  rs2 (bits [24:20]): {rs2}")
    print()
    
    # 格式化操作数
    operands = f"t{td}, (x{rs1}), x{rs2}"
    full_instruction = f"tld.trr.blk.mx48.share {operands}"
    
    print(f"完整指令: {full_instruction}")
    print()
    
    # 检查是否匹配期望
    expected = "tld.trr.blk.mx48.share t0, (x7), x0"
    if full_instruction == expected:
        print("✓ 完美匹配期望结果!")
    else:
        print("✗ 与期望结果不匹配")
        print(f"期望: {expected}")
        print(f"实际: {full_instruction}")
    
    print()
    print("=== 验证位提取 ===")
    
    # 验证每个字段的位提取
    print(f"指令二进制: {instruction:064b}")
    print("位位置:     6666555555555544444444443333333333222222222211111111110000000000")
    print("           3210987654321098765432109876543210987654321098765432109876543210")
    print()
    
    # 手动验证
    td_bits = format((instruction >> 55) & 0xFF, '08b')
    rs1_bits = format((instruction >> 47) & 0x1F, '05b')
    rs2_bits = format((instruction >> 20) & 0x1F, '05b')
    
    print(f"Td字段 [62:55]:  {td_bits} = {td}")
    print(f"rs1字段 [51:47]: {rs1_bits} = {rs1}")
    print(f"rs2字段 [24:20]: {rs2_bits} = {rs2}")

if __name__ == "__main__":
    test_extraction()
