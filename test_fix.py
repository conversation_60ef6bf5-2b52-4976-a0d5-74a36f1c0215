#!/usr/bin/env python3
"""
测试修复后的字段提取是否正确
"""

def test_fixed_extraction():
    # 指令数据
    instruction = 0x8003907b8200647b
    
    print(f"测试指令: 0x{instruction:016x}")
    print()
    
    # 模拟修复后的SystemVerilog字段提取
    # td = instruction_data[32+30:32+23];  // Td field in second word bits [30:23]
    # rs1 = instruction_data[32+19:32+15]; // rs1 field in second word bits [19:15]
    # rs2 = instruction_data[24:20];       // rs3 field in first word bits [24:20]
    
    # 在64位指令中，这相当于：
    # td = instruction_data[62:55];   // bits 32+30 to 32+23 = bits 62 to 55
    # rs1 = instruction_data[51:47];  // bits 32+19 to 32+15 = bits 51 to 47
    # rs2 = instruction_data[24:20];  // bits 24 to 20
    
    td = (instruction >> 55) & 0xFF      # bits [62:55]
    rs1 = (instruction >> 47) & 0x1F     # bits [51:47]
    rs2 = (instruction >> 20) & 0x1F     # bits [24:20]
    
    print("修复后的字段提取:")
    print(f"  td (bits [62:55]):  {td}")
    print(f"  rs1 (bits [51:47]): {rs1}")
    print(f"  rs2 (bits [24:20]): {rs2}")
    print()
    
    # 格式化操作数
    operands = f"t{td}, (x{rs1}), x{rs2}"
    full_instruction = f"tld.trr.blk.mx48.share {operands}"
    
    print(f"完整指令: {full_instruction}")
    print()
    
    # 检查是否匹配期望
    expected = "tld.trr.blk.mx48.share t0, (x7), x0"
    if full_instruction == expected:
        print("✓ 完美匹配期望结果!")
        return True
    else:
        print("✗ 与期望结果不匹配")
        print(f"期望: {expected}")
        print(f"实际: {full_instruction}")
        return False

def test_word_based_extraction():
    # 指令数据
    instruction = 0x8003907b8200647b
    word1 = instruction & 0xFFFFFFFF        # 低32位: 0x8200647b
    word2 = (instruction >> 32) & 0xFFFFFFFF # 高32位: 0x8003907b
    
    print("\n=== 基于word的字段提取测试 ===")
    print(f"Word 1 (低32位): 0x{word1:08x}")
    print(f"Word 2 (高32位): 0x{word2:08x}")
    print()
    
    # 模拟SystemVerilog中的字段提取
    # td = instruction_data[32+30:32+23];  // 相当于 word2[30:23]
    # rs1 = instruction_data[32+19:32+15]; // 相当于 word2[19:15]
    # rs2 = instruction_data[24:20];       // 相当于 word1[24:20]
    
    td = (word2 >> 23) & 0xFF      # word2 bits [30:23]
    rs1 = (word2 >> 15) & 0x1F     # word2 bits [19:15]
    rs2 = (word1 >> 20) & 0x1F     # word1 bits [24:20]
    
    print("基于word的字段提取:")
    print(f"  td (word2[30:23]):  {td}")
    print(f"  rs1 (word2[19:15]): {rs1}")
    print(f"  rs2 (word1[24:20]): {rs2}")
    print()
    
    # 格式化操作数
    operands = f"t{td}, (x{rs1}), x{rs2}"
    full_instruction = f"tld.trr.blk.mx48.share {operands}"
    
    print(f"完整指令: {full_instruction}")
    
    # 检查是否匹配期望
    expected = "tld.trr.blk.mx48.share t0, (x7), x0"
    if full_instruction == expected:
        print("✓ 基于word的提取也正确!")
        return True
    else:
        print("✗ 基于word的提取有问题")
        return False

if __name__ == "__main__":
    print("=== 测试修复后的字段提取 ===")
    
    success1 = test_fixed_extraction()
    success2 = test_word_based_extraction()
    
    print("\n=== 总结 ===")
    if success1 and success2:
        print("✓ 所有测试通过！修复应该是正确的。")
    else:
        print("✗ 测试失败，需要进一步调试。")
