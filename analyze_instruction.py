#!/usr/bin/env python3
"""
Manual analysis of instruction 0x8003907b8200647b
"""

def analyze_instruction():
    # Test instruction
    instruction = 0x8003907b8200647b
    
    # Split into words
    word1 = instruction & 0xFFFFFFFF  # Low 32 bits
    word2 = (instruction >> 32) & 0xFFFFFFFF  # High 32 bits
    
    print(f"Analyzing instruction: 0x{instruction:016x}")
    print(f"Word 1 (low):  0x{word1:08x}")
    print(f"Word 2 (high): 0x{word2:08x}")
    print()
    
    # Extract fields from word1
    ace_op1 = word1 & 0x7F
    lsuop1 = (word1 >> 10) & 0x3
    tuop1 = (word1 >> 12) & 0x7
    rs3 = (word1 >> 20) & 0x1F
    memuop1 = (word1 >> 25) & 0x3F
    
    print("Word 1 fields:")
    print(f"  ACE_OP [6:0]:   0x{ace_op1:02x} ({'TILE' if ace_op1 == 0x7b else 'NOT_TILE'})")
    print(f"  lsuop [11:10]:  {lsuop1}")
    print(f"  tuop [14:12]:   {tuop1}")
    print(f"  rs3 [24:20]:    {rs3}")
    print(f"  memuop [30:25]: {memuop1} (0b{memuop1:06b})")
    print()
    
    # Extract fields from word2
    ace_op2 = word2 & 0x7F
    tuop2 = (word2 >> 12) & 0x7
    rs1 = (word2 >> 15) & 0x1F
    td = (word2 >> 23) & 0xFF
    
    print("Word 2 fields:")
    print(f"  ACE_OP [6:0]:   0x{ace_op2:02x}")
    print(f"  tuop [14:12]:   {tuop2}")
    print(f"  rs1 [19:15]:    {rs1}")
    print(f"  Td [30:23]:     {td}")
    print()
    
    # Check conditions for tld.trr.blk.mx48.share
    print("Condition checks:")
    print(f"  ace_op1 == 0x7b (TILE): {ace_op1 == 0x7b}")
    print(f"  tuop1 == 6 (110): {tuop1 == 6}")
    print(f"  memuop1 == 1 (000001): {memuop1 == 1}")
    print(f"  lsuop1 == 1: {lsuop1 == 1}")
    print(f"  tuop2 == 1 (001): {tuop2 == 1}")
    print()
    
    # Determine instruction type
    if ace_op1 == 0x7b:  # TILE instruction
        if tuop1 == 6:  # tuop_110
            if memuop1 == 1:  # Block memory operations
                if tuop2 == 1:  # Second lane is tuop_001
                    if lsuop1 == 1:
                        instr_name = "tld.trr.blk.mx48.share"
                    elif lsuop1 == 2:
                        instr_name = "tld.trr.blk.mx6.share"
                    elif lsuop1 == 0:
                        instr_name = "tld.trr.blk.share"
                    else:
                        instr_name = "unknown_lsuop"
                else:
                    instr_name = "unknown_second_tuop"
            else:
                instr_name = "unknown_memuop"
        else:
            instr_name = "unknown_tuop"
    else:
        instr_name = "unknown_non_tile"
    
    print(f"Predicted instruction: {instr_name}")
    
    # Format operands
    if instr_name.startswith("tld.trr.blk"):
        operands = f"t{td}, (x{rs1}), x{rs3}"
        full_instruction = f"{instr_name} {operands}"
        print(f"Full assembly: {full_instruction}")
    
    print()
    print("Expected: tld.trr.mx48.share T0, (t2), zero")
    
    # Check if our analysis matches
    if instr_name == "tld.trr.blk.mx48.share" and td == 0 and rs1 == 7 and rs3 == 0:
        print("SUCCESS: Analysis matches expected result!")
        print(f"Note: rs1=7 corresponds to register x7, not t2")
        print(f"The expected result might have a typo - should be (x7) not (t2)")
    else:
        print("MISMATCH: Analysis does not match expected result")

if __name__ == "__main__":
    analyze_instruction()
