
import tile_decoder_pkg::*;

module test_fix;
    initial begin
        logic [31:0] word1, word2;
        logic [63:0] full_instruction;
        instr_collector_t collector;
        string result;
        
        // 测试指令 0x8003907b8200647b
        word1 = 32'h8200647b;  // 低32位
        word2 = 32'h8003907b;  // 高32位
        full_instruction = {word2, word1};
        
        $display("Testing instruction: 0x%016x", full_instruction);
        $display("Word 1 (low):  0x%08x", word1);
        $display("Word 2 (high): 0x%08x", word2);
        
        // 检查是否为tile指令
        if (is_tile_instruction(word1)) begin
            $display("Is tile instruction: YES");
            
            // 初始化收集器
            collector = init_collector(word1);
            $display("Expected length: %s", collector.expected_length.name());
            $display("Complete after word 1: %s", collector.is_complete ? "YES" : "NO");
            
                // 添加第二个字
                collector = add_word_to_collector(collector, word2);
                $display("Complete after word 2: %s", collector.is_complete ? "YES" : "NO");
                
                if (collector.is_complete) begin
                    result = disassemble_instruction(collector.instruction_data, collector.expected_length);
                    $display("Disassembly: %s", result);
                end
            end
        end else begin
            $display("Is tile instruction: NO");
        end
        
        $finish;
    end
endmodule
