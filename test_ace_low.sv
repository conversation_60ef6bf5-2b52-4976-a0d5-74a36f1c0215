// Test for instruction 0xc00e07b to check ace_low issue
import tile_decoder_pkg::*;

module test_ace_low;

    initial begin
        logic [31:0] test_instruction;
        instr_collector_t collector;
        string disasm_result;

        $display("=== Testing instruction 0xc00e07b ===");
        
        // Test the specific instruction: 0xc00e07b
        test_instruction = 32'hc00e07b;
        $display("Testing instruction: 0x%08x", test_instruction);
        
        // Analyze the instruction fields
        $display("ACE_OP [6:0]: %b (%d)", test_instruction[6:0], test_instruction[6:0]);
        $display("tuop [14:12]: %b (%d)", test_instruction[14:12], test_instruction[14:12]);
        $display("memuop [30:25]: %b (%d)", test_instruction[30:25], test_instruction[30:25]);
        $display("lsuop [11:10]: %b (%d)", test_instruction[11:10], test_instruction[11:10]);
        
        // Check if it's recognized as a tile instruction
        if (tile_decoder_pkg::is_tile_instruction(test_instruction)) begin
            $display("✓ Recognized as tile instruction");
            
            // Initialize collector
            collector = tile_decoder_pkg::init_collector(test_instruction);
            $display("Expected length: %0d", collector.expected_length);
            $display("Is complete: %s", collector.is_complete ? "YES" : "NO");
            
            if (collector.is_complete) begin
                // Disassemble the instruction
                disasm_result = tile_decoder_pkg::disassemble_instruction(
                    collector.instruction_data, collector.expected_length);
                $display("Disassembly result: %s", disasm_result);
                
                // Check if it returns ace_low
                if (disasm_result == "ace_low" || disasm_result == "ace_low 0, 0, 0, 0, 0") begin
                    $display("⚠️  WARNING: Instruction returns 'ace_low' - this may be incorrect");
                end
            end else begin
                $display("Instruction is not complete, need more words");
            end
        end else begin
            $display("✗ Not recognized as tile instruction");
        end
        
        $display("=== Test Complete ===");
    end

endmodule
