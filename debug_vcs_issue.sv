// Debug VCS issue - why is field extraction still wrong?
// Testing instruction 0x8003907b8200647b

`timescale 1ns/1ps

import tile_decoder_pkg::*;

module debug_vcs_issue;

    initial begin
        logic [31:0] word1, word2;
        logic [63:0] full_instruction;
        instr_collector_t collector;
        string result, instr_name, operands;
        
        $display("=== Debug VCS Issue for 0x8003907b8200647b ===");
        $display("");
        
        // Test instruction 0x8003907b8200647b
        word1 = 32'h8200647b;  // Low 32 bits
        word2 = 32'h8003907b;  // High 32 bits
        full_instruction = {word2, word1};
        
        $display("Test Data:");
        $display("  Full instruction: 0x%016x", full_instruction);
        $display("  Word 1 (low):     0x%08x", word1);
        $display("  Word 2 (high):    0x%08x", word2);
        $display("");
        
        // Initialize and collect instruction
        collector = tile_decoder_pkg::init_collector(word1);
        if (!collector.is_complete) begin
            collector = tile_decoder_pkg::add_word_to_collector(collector, word2);
        end
        
        $display("Collector state:");
        $display("  instruction_data: 0x%032x", collector.instruction_data);
        $display("  length: %s", collector.expected_length.name());
        $display("  complete: %s", collector.is_complete ? "YES" : "NO");
        $display("");
        
        if (collector.is_complete) begin
            // Step 1: Test instruction name extraction
            $display("=== Step 1: Instruction Name Extraction ===");
            instr_name = tile_decoder_pkg::extract_instruction_name(
                collector.instruction_data, collector.expected_length);
            $display("  Extracted name: '%s'", instr_name);
            $display("  Name length: %0d", instr_name.len());
            
            // Check string matching
            $display("  String analysis:");
            $display("    instr_name.substr(0, 3): '%s'", instr_name.substr(0, 3));
            $display("    instr_name.substr(4, 3): '%s'", instr_name.substr(4, 3));
            $display("    substr(0,3) == 'tld': %s", instr_name.substr(0, 3) == "tld" ? "TRUE" : "FALSE");
            $display("    substr(4,3) == 'trr': %s", instr_name.substr(4, 3) == "trr" ? "TRUE" : "FALSE");
            $display("");
            
            // Step 2: Test operand formatting
            $display("=== Step 2: Operand Formatting ===");
            operands = tile_decoder_pkg::format_operands(
                collector.instruction_data, collector.expected_length, instr_name);
            $display("  Extracted operands: '%s'", operands);
            $display("");
            
            // Step 3: Manual field extraction to debug
            $display("=== Step 3: Manual Field Extraction Debug ===");
            
            logic [7:0] debug_td;
            logic [4:0] debug_rs1, debug_rs2;
            
            // Test both old and new field positions
            $display("  OLD (incorrect) positions:");
            debug_td = collector.instruction_data[62:55];
            debug_rs1 = collector.instruction_data[51:47];
            debug_rs2 = collector.instruction_data[24:20];
            $display("    td [62:55]:  %0d", debug_td);
            $display("    rs1 [51:47]: %0d", debug_rs1);
            $display("    rs2 [24:20]: %0d", debug_rs2);
            $display("    OLD result: t%0d, (x%0d), x%0d", debug_td, debug_rs1, debug_rs2);
            $display("");
            
            $display("  NEW (correct) positions:");
            debug_td = collector.instruction_data[32+30:32+23];
            debug_rs1 = collector.instruction_data[32+19:32+15];
            debug_rs2 = collector.instruction_data[24:20];
            $display("    td [32+30:32+23]:  %0d", debug_td);
            $display("    rs1 [32+19:32+15]: %0d", debug_rs1);
            $display("    rs2 [24:20]:       %0d", debug_rs2);
            $display("    NEW result: t%0d, (x%0d), x%0d", debug_td, debug_rs1, debug_rs2);
            $display("");
            
            // Step 4: Check which path is being taken in format_operands
            $display("=== Step 4: Format Path Analysis ===");
            if (instr_name.substr(0, 2) == "tld" || instr_name.substr(0, 2) == "tst") begin
                $display("  ✓ Entered tld/tst branch");
                if (instr_name.substr(4, 3) == "trr") begin
                    $display("  ✓ Entered trr sub-branch - should use FIXED positions");
                    $display("  This should produce: t0, (x7), x0");
                end else begin
                    $display("  ✗ Did NOT enter trr sub-branch");
                    $display("  This explains why we get wrong values!");
                    $display("  instr_name.substr(4, 3) = '%s'", instr_name.substr(4, 3));
                end
            end else begin
                $display("  ✗ Did NOT enter tld/tst branch");
            end
            $display("");
            
            // Step 5: Full disassembly
            $display("=== Step 5: Full Disassembly ===");
            result = tile_decoder_pkg::disassemble_instruction(
                collector.instruction_data, collector.expected_length);
            $display("  Final result: '%s'", result);
            $display("  Expected:     'tld.trr.blk.mx48.share t0, (x7), x0'");
            
            if (result == "tld.trr.blk.mx48.share t0, (x7), x0") begin
                $display("  ✓ SUCCESS: Perfect match!");
            end else begin
                $display("  ✗ FAILURE: Mismatch found");
                $display("  This indicates the fix is not working in VCS");
            end
            
        end else begin
            $display("ERROR: Instruction collection not complete!");
        end
        
        $display("");
        $display("=== Debug Complete ===");
        $finish;
    end

endmodule
