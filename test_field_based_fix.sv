// Test the field-based fix for instruction 0x8003907b8200647b
// This version uses field values instead of string matching

`timescale 1ns/1ps

import tile_decoder_pkg::*;

module test_field_based_fix;

    initial begin
        logic [31:0] word1, word2;
        instr_collector_t collector;
        string result;
        
        $display("=== Testing Field-Based Fix for 0x8003907b8200647b ===");
        $display("");
        
        // Test instruction 0x8003907b8200647b
        word1 = 32'h8200647b;  // Low 32 bits
        word2 = 32'h8003907b;  // High 32 bits
        
        $display("Test Data:");
        $display("  Word 1 (low):  0x%08x", word1);
        $display("  Word 2 (high): 0x%08x", word2);
        $display("");
        
        // Initialize and collect instruction
        collector = tile_decoder_pkg::init_collector(word1);
        if (!collector.is_complete) begin
            collector = tile_decoder_pkg::add_word_to_collector(collector, word2);
        end
        
        $display("Instruction Analysis:");
        $display("  instruction_data: 0x%032x", collector.instruction_data);
        $display("  length: %s", collector.expected_length.name());
        $display("");
        
        if (collector.is_complete) begin
            // Analyze the instruction fields that should trigger our fix
            logic [2:0] tuop_first = collector.instruction_data[14:12];
            logic [5:0] memuop_field = collector.instruction_data[30:25];
            logic [2:0] tuop_second = collector.instruction_data[32+14:32+12];
            
            $display("Field Analysis:");
            $display("  tuop_first [14:12]:    %0d (should be 6/110)", tuop_first);
            $display("  memuop [30:25]:        %0d (should be 1/000001)", memuop_field);
            $display("  tuop_second [46:44]:   %0d (should be 1/001)", tuop_second);
            $display("");
            
            // Check if our field-based condition will trigger
            if (tuop_first == 3'b110 && memuop_field == 6'b000001 && tuop_second == 3'b001) begin
                $display("✓ Field-based condition MATCHES - should use corrected field positions");
                
                // Show what the corrected field extraction should produce
                logic [7:0] expected_td = collector.instruction_data[32+30:32+23];
                logic [4:0] expected_rs1 = collector.instruction_data[32+19:32+15];
                logic [4:0] expected_rs2 = collector.instruction_data[24:20];
                
                $display("  Expected field values:");
                $display("    td [32+30:32+23]:  %0d", expected_td);
                $display("    rs1 [32+19:32+15]: %0d", expected_rs1);
                $display("    rs2 [24:20]:       %0d", expected_rs2);
                $display("  Expected result: t%0d, (x%0d), x%0d", expected_td, expected_rs1, expected_rs2);
            end else begin
                $display("✗ Field-based condition does NOT match - will use wrong field positions");
                $display("  This means our fix won't trigger!");
            end
            $display("");
            
            // Test the actual disassembly
            $display("Actual Disassembly:");
            result = tile_decoder_pkg::disassemble_instruction(
                collector.instruction_data, collector.expected_length);
            $display("  Result: '%s'", result);
            $display("  Expected: 'tld.trr.blk.mx48.share t0, (x7), x0'");
            $display("");
            
            // Final verification
            if (result == "tld.trr.blk.mx48.share t0, (x7), x0") begin
                $display("✓ SUCCESS: Field-based fix is working!");
            end else begin
                $display("✗ FAILURE: Field-based fix is not working");
                $display("  This suggests there might be another issue");
            end
            
        end else begin
            $display("ERROR: Instruction collection not complete!");
        end
        
        $display("");
        $display("=== Test Complete ===");
        $finish;
    end

endmodule
