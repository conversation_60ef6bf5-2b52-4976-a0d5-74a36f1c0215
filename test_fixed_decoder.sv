// Test script for the fixed instruction decoder
// Testing instruction 0x8003907b8200647b

`timescale 1ns/1ps

import tile_decoder_pkg::*;

module test_fixed_decoder;

    initial begin
        logic [31:0] word1, word2;
        logic [63:0] full_instruction;
        instr_collector_t collector;
        string result;
        
        $display("=== Testing Fixed Decoder for 0x8003907b8200647b ===");
        $display("");
        
        // Test instruction 0x8003907b8200647b
        word1 = 32'h8200647b;  // Low 32 bits
        word2 = 32'h8003907b;  // High 32 bits
        full_instruction = {word2, word1};
        
        $display("Full instruction: 0x%016x", full_instruction);
        $display("Word 1 (low):     0x%08x", word1);
        $display("Word 2 (high):    0x%08x", word2);
        $display("");
        
        // Initialize collector with first word
        collector = tile_decoder_pkg::init_collector(word1);
        $display("After first word:");
        $display("  Is tile: %s", collector.is_tile_instr ? "YES" : "NO");
        $display("  Expected length: %s", collector.expected_length.name());
        $display("  Complete: %s", collector.is_complete ? "YES" : "NO");
        $display("");
        
        // Add second word
        if (!collector.is_complete) begin
            collector = tile_decoder_pkg::add_word_to_collector(collector, word2);
            $display("After second word:");
            $display("  Complete: %s", collector.is_complete ? "YES" : "NO");
            $display("");
        end
        
        // Disassemble
        if (collector.is_complete) begin
            result = tile_decoder_pkg::disassemble_instruction(
                collector.instruction_data, collector.expected_length);
            $display("Disassembly result: %s", result);
            $display("");
            
            // Check if it matches expected
            if (result == "tld.trr.blk.mx48.share t0, (x7), x0") begin
                $display("✓ SUCCESS: Perfect match with expected result!");
            end else begin
                $display("✗ FAILURE: Does not match expected result");
                $display("  Expected: tld.trr.blk.mx48.share t0, (x7), x0");
                $display("  Actual:   %s", result);
            end
            
            // Debug field extraction
            $display("");
            $display("Debug field extraction:");
            $display("  instruction_data: 0x%032x", collector.instruction_data);
            
            // Manual field extraction to verify
            logic [7:0] debug_td;
            logic [4:0] debug_rs1, debug_rs2;
            
            // Using the fixed field positions
            debug_td = collector.instruction_data[32+30:32+23];  // Td field in second word bits [30:23]
            debug_rs1 = collector.instruction_data[32+19:32+15]; // rs1 field in second word bits [19:15]
            debug_rs2 = collector.instruction_data[24:20];       // rs3 field in first word bits [24:20]
            
            $display("  debug_td (word2[30:23]):  %0d", debug_td);
            $display("  debug_rs1 (word2[19:15]): %0d", debug_rs1);
            $display("  debug_rs2 (word1[24:20]): %0d", debug_rs2);
            
            if (debug_td == 0 && debug_rs1 == 7 && debug_rs2 == 0) begin
                $display("✓ Field extraction is correct!");
            end else begin
                $display("✗ Field extraction has issues");
            end
            
        end else begin
            $display("ERROR: Instruction collection not complete!");
        end
        
        $display("\n=== Test Complete ===");
    end

endmodule
