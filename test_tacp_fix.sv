// Test the fix for tacp instruction collection
// Verify that 128-bit instructions can be properly collected

`include "tile_instruction_decoder.sv"

import tile_decoder_pkg::*;

module test_tacp_fix;

    logic [31:0] words[4];
    instr_collector_t collector;
    string result;
    logic [31:0] test_64bit_0, test_64bit_1;
    
    initial begin
        $display("=== Testing TACP Instruction Fix ===");
        
        // Test the specific instruction sequence from the user
        words[0] = 32'h380ee0fb;  // First word
        words[1] = 32'h8e05017b;  // Second word
        words[2] = 32'h00000000;  // Third word (placeholder)
        words[3] = 32'h00000000;  // Fourth word (placeholder)
        
        $display("\nTesting 128-bit tacp instruction:");
        $display("Word 0: 0x%08x", words[0]);
        $display("Word 1: 0x%08x", words[1]);
        $display("Word 2: 0x%08x", words[2]);
        $display("Word 3: 0x%08x", words[3]);
        
        // Initialize collector with first word
        collector = tile_decoder_pkg::init_collector(words[0]);
        $display("\nAfter init:");
        $display("  collected_words: %d", collector.collected_words);
        $display("  expected_length: %s", 
                collector.expected_length == INSTR_128BIT ? "INSTR_128BIT" : "OTHER");
        $display("  is_complete: %s", collector.is_complete ? "YES" : "NO");
        
        // Add remaining words one by one
        collector = tile_decoder_pkg::add_word_to_collector(collector, words[1]);
        $display("\nAfter adding word 1:");
        $display("  collected_words: %d", collector.collected_words);
        $display("  is_complete: %s", collector.is_complete ? "YES" : "NO");

        collector = tile_decoder_pkg::add_word_to_collector(collector, words[2]);
        $display("\nAfter adding word 2:");
        $display("  collected_words: %d", collector.collected_words);
        $display("  is_complete: %s", collector.is_complete ? "YES" : "NO");

        collector = tile_decoder_pkg::add_word_to_collector(collector, words[3]);
        $display("\nAfter adding word 3:");
        $display("  collected_words: %d", collector.collected_words);
        $display("  is_complete: %s", collector.is_complete ? "YES" : "NO");

        if (collector.is_complete) begin
            result = tile_decoder_pkg::disassemble_instruction(
                collector.instruction_data, collector.expected_length);
            $display("  Disassembly: %s", result);
        end
        
        // Verify the fix
        if (collector.is_complete && collector.collected_words == 4) begin
            $display("\n✓ SUCCESS: 128-bit instruction collection works correctly!");
            $display("  Final collected_words: %d", collector.collected_words);
            $display("  Final is_complete: %s", collector.is_complete ? "YES" : "NO");
        end else begin
            $display("\n✗ FAILURE: 128-bit instruction collection still broken");
            $display("  Final collected_words: %d", collector.collected_words);
            $display("  Final is_complete: %s", collector.is_complete ? "YES" : "NO");
        end
        
        // Test edge cases
        $display("\n=== Testing Edge Cases ===");

        // Test 64-bit instruction (should still work)
        test_64bit_0 = 32'h0000607b;  // 64-bit instruction
        test_64bit_1 = 32'h8000007b;

        collector = tile_decoder_pkg::init_collector(test_64bit_0);
        $display("\n64-bit instruction test:");
        $display("  After init: collected_words=%d, is_complete=%s",
                collector.collected_words, collector.is_complete ? "YES" : "NO");

        collector = tile_decoder_pkg::add_word_to_collector(collector, test_64bit_1);
        $display("  After word 2: collected_words=%d, is_complete=%s", 
                collector.collected_words, collector.is_complete ? "YES" : "NO");
        
        if (collector.is_complete) begin
            $display("✓ 64-bit instruction collection still works");
        end else begin
            $display("✗ 64-bit instruction collection broken");
        end
        
        $display("\n=== Test Complete ===");
    end

endmodule
