// Comprehensive test for all instruction bit widths
// Tests 32-bit, 64-bit, 96-bit, and 128-bit instructions

`timescale 1ns/1ps

import tile_decoder_pkg::*;

module test_all_bitwidths;

    // Test data structure
    typedef struct {
        logic [127:0] instruction_data;
        int bit_width;
        string expected_result;
        string description;
    } test_case_t;

    // Test cases for all bit widths
    test_case_t test_cases[] = '{
        // 32-bit instructions
        '{
            instruction_data: 128'h0000407b,
            bit_width: 32,
            expected_result: "tcsrw.i x0, 0x0, x0",
            description: "32-bit CSR instruction"
        },
        
        // 64-bit instructions
        '{
            instruction_data: 128'h8000007b0000607b,
            bit_width: 64,
            expected_result: "tld.trii.linear.u32.global t0, (x0)",
            description: "64-bit standard memory load"
        },
        '{
            instruction_data: 128'h8003907b8200647b,
            bit_width: 64,
            expected_result: "tld.trr.blk.mx48.share t0, (x7), x0",
            description: "64-bit block memory load"
        },
        
        // 96-bit instructions
        '{
            instruction_data: 128'hf260707b8000007b0000697b,
            bit_width: 96,
            expected_result: "tld.trvi.asp.index.u32.global t0, (x0)",
            description: "96-bit indexed memory load"
        },
        
        // 128-bit instructions
        '{
            instruction_data: 128'hf260707bf260707b8000047b3800617b,
            bit_width: 128,
            expected_result: "tacp.rvv.asp2.dsttm 0, 0, x0, 0, 0, 0, 0",
            description: "128-bit tile copy"
        }
    };

    // Test each instruction
    task automatic test_instruction(input test_case_t test_case);
        logic [31:0] words[4];
        instr_collector_t collector;
        logic collector_active = 1'b0;
        string result;
        int num_words;
        
        $display("Testing: %s", test_case.description);
        $display("  Input: 0x%0x (%0d-bit)", test_case.instruction_data, test_case.bit_width);
        
        // Split instruction into 32-bit words
        num_words = test_case.bit_width / 32;
        for (int i = 0; i < num_words; i++) begin
            words[i] = test_case.instruction_data[i*32 +: 32];
            $display("  Word %0d: 0x%08x", i, words[i]);
        end
        
        // Process words sequentially
        for (int i = 0; i < num_words; i++) begin
            if (!collector_active) begin
                // Start new instruction
                if (is_tile_instruction(words[i])) begin
                    collector = init_collector(words[i]);
                    collector_active = 1'b1;
                    
                    $display("  -> Started instruction collection");
                    $display("     Expected length: %s", collector.expected_length.name());
                    $display("     Complete: %s", collector.is_complete ? "YES" : "NO");
                    
                    if (collector.is_complete) begin
                        // 32-bit instruction complete
                        result = disassemble_instruction(collector.instruction_data, collector.expected_length);
                        $display("  -> Complete after word %0d", i);
                        break;
                    end
                end else begin
                    $display("  -> ERROR: Not recognized as tile instruction");
                    return;
                end
            end else begin
                // Add word to existing instruction
                collector = add_word_to_collector(collector, words[i]);
                $display("  -> Added word %0d, Complete: %s", i, collector.is_complete ? "YES" : "NO");
                
                if (collector.is_complete) begin
                    result = disassemble_instruction(collector.instruction_data, collector.expected_length);
                    $display("  -> Complete after word %0d", i);
                    break;
                end
            end
        end
        
        // Check result
        $display("  Expected: %s", test_case.expected_result);
        $display("  Actual:   %s", result);
        
        if (result == test_case.expected_result) begin
            $display("  Result: PASS ✓");
        end else begin
            $display("  Result: FAIL ✗");
        end
        
        $display("");
    endtask

    // Test instruction length detection
    task automatic test_length_detection();
        logic [31:0] test_words[] = '{
            32'h0000407b,  // 32-bit CSR
            32'h0000607b,  // 64-bit memory
            32'h0000697b,  // 96-bit indexed (first word)
            32'h3800617b   // 128-bit tile copy (first word)
        };
        
        string expected_lengths[] = '{"INSTR_32BIT", "INSTR_64BIT", "INSTR_96BIT", "INSTR_128BIT"};
        
        $display("=== Instruction Length Detection Test ===");
        
        foreach (test_words[i]) begin
            instr_length_e detected_length = get_instruction_length(test_words[i]);
            logic is_tile = is_tile_instruction(test_words[i]);
            
            $display("Word 0x%08x:", test_words[i]);
            $display("  Is tile: %s", is_tile ? "YES" : "NO");
            $display("  Detected: %s", detected_length.name());
            $display("  Expected: %s", expected_lengths[i]);
            $display("  Result: %s", (detected_length.name() == expected_lengths[i]) ? "PASS ✓" : "FAIL ✗");
            $display("");
        end
    endtask

    // Test field extraction
    task automatic test_field_extraction();
        logic [127:0] test_instruction = 128'h8003907b8200647b; // 64-bit block memory
        
        $display("=== Field Extraction Test ===");
        $display("Test instruction: 0x%016x", test_instruction[63:0]);
        
        // Extract fields from first word
        logic [31:0] word1 = test_instruction[31:0];
        logic [6:0] ace_op1 = word1[6:0];
        logic [2:0] tuop1 = word1[14:12];
        logic [1:0] lsuop1 = word1[11:10];
        logic [5:0] memuop1 = word1[30:25];
        logic [4:0] rs3 = word1[24:20];
        
        $display("Word 1 fields:");
        $display("  ACE_OP: 0x%02x", ace_op1);
        $display("  tuop: %0d", tuop1);
        $display("  lsuop: %0d", lsuop1);
        $display("  memuop: %0d", memuop1);
        $display("  rs3: %0d", rs3);
        
        // Extract fields from second word
        logic [31:0] word2 = test_instruction[63:32];
        logic [6:0] ace_op2 = word2[6:0];
        logic [2:0] tuop2 = word2[14:12];
        logic [4:0] rs1 = word2[19:15];
        logic [7:0] td = word2[30:23];
        
        $display("Word 2 fields:");
        $display("  ACE_OP: 0x%02x", ace_op2);
        $display("  tuop: %0d", tuop2);
        $display("  rs1: %0d", rs1);
        $display("  Td: %0d", td);
        
        // Verify expected values
        $display("Verification:");
        $display("  Both ACE_OP = 0x7b: %s", (ace_op1 == 7'h7b && ace_op2 == 7'h7b) ? "PASS ✓" : "FAIL ✗");
        $display("  tuop1 = 6: %s", (tuop1 == 3'd6) ? "PASS ✓" : "FAIL ✗");
        $display("  tuop2 = 1: %s", (tuop2 == 3'd1) ? "PASS ✓" : "FAIL ✗");
        $display("  memuop1 = 1: %s", (memuop1 == 6'd1) ? "PASS ✓" : "FAIL ✗");
        $display("  lsuop1 = 1: %s", (lsuop1 == 2'd1) ? "PASS ✓" : "FAIL ✗");
        $display("");
    endtask

    // Main test sequence
    initial begin
        $display("Comprehensive Tile Extension ISA Decoder Test");
        $display("Testing all instruction bit widths (32/64/96/128)");
        $display("=" * 60);
        $display("");
        
        // Test length detection
        test_length_detection();
        
        // Test field extraction
        test_field_extraction();
        
        // Test each instruction type
        $display("=== Individual Instruction Tests ===");
        foreach (test_cases[i]) begin
            test_instruction(test_cases[i]);
        end
        
        $display("=== Test Summary ===");
        $display("All bit widths tested: 32, 64, 96, 128 bits");
        $display("Check results above for PASS/FAIL status");
        $display("");
        
        $finish;
    end

endmodule
