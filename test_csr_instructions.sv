// Test for CSR instructions
// Verifies that all CSR instructions are correctly identified as 32-bit

`timescale 1ns/1ps

import tile_decoder_pkg::*;

module test_csr_instructions;

    // CSR test cases
    typedef struct {
        logic [31:0] instruction;
        string expected_name;
        string expected_operands;
        string description;
    } csr_test_t;

    csr_test_t csr_tests[] = '{
        // Only test the basic CSR instructions that match Python patterns
        // tcsrw.i - write immediate (rw=00)
        '{
            instruction: 32'h0000407b,
            expected_name: "tcsrw.i",
            expected_operands: "0x0",
            description: "tcsrw.i basic pattern"
        },

        // tcsrr.r - read to register (rw=01)
        '{
            instruction: 32'h4000407b,
            expected_name: "tcsrr.r",
            expected_operands: "x0",
            description: "tcsrr.r basic pattern"
        },

        // tcsrw.r - write from register (rw=10)
        '{
            instruction: 32'h8000407b,
            expected_name: "tcsrw.r",
            expected_operands: "x0",
            description: "tcsrw.r basic pattern"
        }
    };

    // Test each CSR instruction
    task automatic test_csr_instruction(input csr_test_t test_case);
        instr_collector_t collector;
        string result_name, result_operands, full_result;
        instr_length_e detected_length;
        logic is_tile;
        
        $display("Testing: %s", test_case.description);
        $display("  Instruction: 0x%08x", test_case.instruction);
        
        // Analyze instruction fields
        logic [6:0] ace_op = test_case.instruction[6:0];
        logic [2:0] tuop = test_case.instruction[14:12];
        logic [1:0] rw = test_case.instruction[31:30];
        logic [8:0] csr_addr = test_case.instruction[28:20];
        logic [4:0] rd = test_case.instruction[11:7];
        logic [4:0] rs1_imm = test_case.instruction[19:15];
        
        $display("  Fields: ACE_OP=0x%02x, tuop=%0d, rw=%0d, csr_addr=0x%03x, rd=%0d, rs1_imm=%0d",
                ace_op, tuop, rw, csr_addr, rd, rs1_imm);
        
        // Test basic functions
        is_tile = is_tile_instruction(test_case.instruction);
        detected_length = get_instruction_length(test_case.instruction);
        
        $display("  is_tile_instruction: %s", is_tile ? "TRUE" : "FALSE");
        $display("  get_instruction_length: %s", detected_length.name());
        
        // Test collector
        if (is_tile) begin
            collector = init_collector(test_case.instruction);
            $display("  Collector initialized:");
            $display("    expected_length: %s", collector.expected_length.name());
            $display("    is_complete: %s", collector.is_complete ? "TRUE" : "FALSE");
            
            if (collector.is_complete) begin
                full_result = disassemble_instruction(collector.instruction_data, collector.expected_length);
                $display("  Disassembly: %s", full_result);
                
                // Parse result to check name and operands
                // Expected format: "name operands"
                int space_pos = 0;
                for (int i = 0; i < full_result.len(); i++) begin
                    if (full_result.getc(i) == " ") begin
                        space_pos = i;
                        break;
                    end
                end
                
                if (space_pos > 0) begin
                    result_name = full_result.substr(0, space_pos-1);
                    result_operands = full_result.substr(space_pos+1, full_result.len()-1);
                end else begin
                    result_name = full_result;
                    result_operands = "";
                end
                
                $display("  Parsed name: '%s'", result_name);
                $display("  Parsed operands: '%s'", result_operands);
                
                // Check results
                $display("  Expected name: '%s'", test_case.expected_name);
                $display("  Expected operands: '%s'", test_case.expected_operands);
                
                if (result_name == test_case.expected_name && 
                    result_operands == test_case.expected_operands) begin
                    $display("  Result: PASS ✓");
                end else begin
                    $display("  Result: FAIL ✗");
                    if (result_name != test_case.expected_name) begin
                        $display("    Name mismatch: got '%s', expected '%s'", result_name, test_case.expected_name);
                    end
                    if (result_operands != test_case.expected_operands) begin
                        $display("    Operands mismatch: got '%s', expected '%s'", result_operands, test_case.expected_operands);
                    end
                end
            end else begin
                $display("  ERROR: 32-bit instruction should be complete immediately");
            end
        end else begin
            $display("  ERROR: CSR instruction should be recognized as tile instruction");
        end
        
        $display("");
    endtask

    // Test basic CSR properties
    task automatic test_csr_properties();
        $display("=== CSR Instruction Properties Test ===");
        
        // Verify all CSR instructions are 32-bit
        logic [31:0] csr_samples[] = '{
            32'h0000407b,  // tcsrw.i
            32'h4000407b,  // tcsrr.r  
            32'h8000407b   // tcsrw.r
        };
        
        foreach (csr_samples[i]) begin
            instr_length_e length = get_instruction_length(csr_samples[i]);
            logic is_tile = is_tile_instruction(csr_samples[i]);
            
            $display("CSR sample 0x%08x:", csr_samples[i]);
            $display("  Is tile: %s", is_tile ? "YES" : "NO");
            $display("  Length: %s", length.name());
            $display("  32-bit check: %s", (length == INSTR_32BIT) ? "PASS ✓" : "FAIL ✗");
        end
        
        $display("");
    endtask

    // Main test
    initial begin
        $display("CSR Instructions Test");
        $display("=" * 50);
        $display("");
        
        // Test basic properties
        test_csr_properties();
        
        // Test each CSR instruction
        $display("=== Individual CSR Instruction Tests ===");
        foreach (csr_tests[i]) begin
            test_csr_instruction(csr_tests[i]);
        end
        
        $display("=== CSR Test Summary ===");
        $display("- All CSR instructions should be 32-bit");
        $display("- All CSR instructions should be recognized as tile instructions");
        $display("- CSR operand formats should match expected patterns");
        $display("");
        
        $finish;
    end

endmodule
