// Test for the fixed instruction 0x8003907b8200647b
// Should now correctly decode as tld.trr.blk.mx48.share

`timescale 1ns/1ps

import tile_decoder_pkg::*;

module test_fixed_instruction;

    initial begin
        logic [31:0] word1, word2;
        logic [63:0] full_instruction;
        instr_collector_t collector;
        string result;
        
        $display("=== Testing Fixed Instruction 0x8003907b8200647b ===");
        $display("");
        
        // Test instruction 0x8003907b8200647b
        word1 = 32'h8200647b;  // Low 32 bits
        word2 = 32'h8003907b;  // High 32 bits
        full_instruction = {word2, word1};
        
        $display("Full instruction: 0x%016x", full_instruction);
        $display("Word 1 (low):     0x%08x", word1);
        $display("Word 2 (high):    0x%08x", word2);
        $display("");
        
        // Test step by step
        $display("Step 1: Check if tile instruction");
        if (tile_decoder_pkg::is_tile_instruction(word1)) begin
            $display("  ✓ Recognized as tile instruction");
        end else begin
            $display("  ✗ NOT recognized as tile instruction");
            $finish;
        end
        
        $display("Step 2: Get instruction length");
        instr_length_e length = tile_decoder_pkg::get_instruction_length(word1);
        $display("  Length: %s", length.name());
        if (length == INSTR_64BIT) begin
            $display("  ✓ Correct length (64-bit)");
        end else begin
            $display("  ✗ Wrong length, expected 64-bit");
            $finish;
        end
        
        $display("Step 3: Initialize collector");
        collector = tile_decoder_pkg::init_collector(word1);
        $display("  Collected words: %0d", collector.collected_words);
        $display("  Is complete: %s", collector.is_complete ? "YES" : "NO");
        
        $display("Step 4: Add second word");
        collector = tile_decoder_pkg::add_word_to_collector(collector, word2);
        $display("  Collected words: %0d", collector.collected_words);
        $display("  Is complete: %s", collector.is_complete ? "YES" : "NO");
        
        if (!collector.is_complete) begin
            $display("  ✗ Instruction collection failed");
            $finish;
        end
        
        $display("Step 5: Disassemble instruction");
        result = tile_decoder_pkg::disassemble_instruction(
            collector.instruction_data, collector.expected_length);
        $display("  Result: %s", result);
        
        // Check if result is correct
        if (result == "tld.trr.blk.mx48.share t0, (x7), x0") begin
            $display("  ✓ PERFECT MATCH!");
        end else if (result.substr(0, 20) == "tld.trr.blk.mx48.share") begin
            $display("  ✓ Instruction name correct, checking operands...");
        end else if (result == "unknown_tile") begin
            $display("  ✗ Still returning unknown_tile - fix not working");
        end else begin
            $display("  ? Unexpected result: %s", result);
        end
        
        $display("");
        $display("Expected: tld.trr.blk.mx48.share t0, (x7), x0");
        $display("Actual:   %s", result);
        
        if (result.substr(0, 20) == "tld.trr.blk.mx48.share") begin
            $display("");
            $display("SUCCESS: Instruction correctly identified!");
            $display("The fix has resolved the unknown_tile issue.");
        end else begin
            $display("");
            $display("FAILURE: Instruction still not correctly identified.");
        end
        
        $display("\n=== Test Complete ===");
    end

endmodule
