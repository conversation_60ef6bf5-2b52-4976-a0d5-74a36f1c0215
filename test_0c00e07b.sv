// Test for instruction 0x0C00E07B length detection issue
import tile_decoder_pkg::*;

module test_0c00e07b;

    initial begin
        logic [31:0] test_instruction;
        instr_collector_t collector;
        string disasm_result;

        $display("=== Testing instruction 0x0C00E07B ===");
        
        // Test the problematic instruction: 0x0C00E07B
        test_instruction = 32'h0C00E07B;
        $display("Testing instruction: 0x%08x", test_instruction);
        
        // Analyze fields
        $display("Field analysis:");
        $display("  ACE_OP [6:0]: %b (%d)", test_instruction[6:0], test_instruction[6:0]);
        $display("  tuop [14:12]: %b (%d)", test_instruction[14:12], test_instruction[14:12]);
        $display("  lsuop [11:10]: %b (%d)", test_instruction[11:10], test_instruction[11:10]);
        $display("  memuop [30:25]: %b (%d)", test_instruction[30:25], test_instruction[30:25]);
        $display("  rs2en [8]: %b", test_instruction[8]);
        $display("  rs3en [31]: %b", test_instruction[31]);
        
        if (tile_decoder_pkg::is_tile_instruction(test_instruction)) begin
            $display("✓ Recognized as tile instruction");
            
            collector = tile_decoder_pkg::init_collector(test_instruction);
            $display("Expected length: %0d", collector.expected_length);
            $display("Is complete: %s", collector.is_complete ? "YES" : "NO");
            
            if (collector.expected_length == tile_decoder_pkg::INSTR_32BIT) begin
                $display("✗ PROBLEM: Detected as 32-bit, but should be 64-bit");
                
                if (collector.is_complete) begin
                    disasm_result = tile_decoder_pkg::disassemble_instruction(
                        collector.instruction_data, collector.expected_length);
                    $display("  Current result: %s", disasm_result);
                    
                    if (disasm_result == "ace_low") begin
                        $display("  ✗ CONFIRMED: Returns 'ace_low' which is not a real instruction");
                    end
                end
            end else begin
                $display("✓ Correctly detected as multi-word instruction");
            end
        end else begin
            $display("✗ Not recognized as tile instruction");
        end
        
        $display("\n=== Analysis ===");
        $display("This instruction has tuop=110 and memuop=6 (000110)");
        $display("Current logic only recognizes memuop 0, 1, 28 as multi-lane");
        $display("But memuop=6 should also be a 64-bit instruction");
        $display("Need to fix the length detection logic");
        
        $display("\n=== Test Complete ===");
    end

endmodule
