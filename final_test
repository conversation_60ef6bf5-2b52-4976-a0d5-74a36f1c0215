#! /usr/bin/vvp
:ivl_version "11.0 (stable)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/system.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_sys.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_textio.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2005_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/va_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2009.vpi";
S_0x55a5f7e327f0 .scope package, "$unit" "$unit" 2 1;
 .timescale 0 0;
S_0x55a5f7e029c0 .scope package, "tile_decoder_pkg" "tile_decoder_pkg" 3 5;
 .timescale 0 0;
P_0x55a5f7e02b50 .param/l "TILE_ACE_OP" 0 3 16, C4<1111011>;
enum0x55a5f7d898e0 .enum4 (2)
   "INSTR_32BIT" 2'b00,
   "INSTR_64BIT" 2'b01,
   "INSTR_96BIT" 2'b10,
   "INSTR_128BIT" 2'b11
 ;
S_0x55a5f7e08160 .scope autofunction.vec4.s134, "add_word_to_collector" "add_word_to_collector" 3 133, 3 133 0, S_0x55a5f7e029c0;
 .timescale 0 0;
; Variable add_word_to_collector is vec4 return value of scope S_0x55a5f7e08160
v0x55a5f7d89fa0_0 .var "collector", 133 0;
v0x55a5f7e7a8d0_0 .var "new_collector", 133 0;
v0x55a5f7e7a990_0 .var "word", 31 0;
TD_tile_decoder_pkg.add_word_to_collector ;
    %load/vec4 v0x55a5f7d89fa0_0;
    %store/vec4 v0x55a5f7e7a8d0_0, 0, 134;
    %load/vec4 v0x55a5f7d89fa0_0;
    %parti/u 1, 1, 32;
    %nor/r;
    %load/vec4 v0x55a5f7d89fa0_0;
    %parti/u 2, 4, 32;
    %pad/u 32;
    %cmpi/u 4, 0, 32;
    %flag_get/vec4 5;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.0, 8;
    %load/vec4 v0x55a5f7d89fa0_0;
    %parti/u 2, 4, 32;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_0.2, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_0.3, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_0.4, 6;
    %jmp T_0.5;
T_0.2 ;
    %load/vec4 v0x55a5f7e7a990_0;
    %ix/load 4, 38, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55a5f7e7a8d0_0, 4, 32;
    %jmp T_0.5;
T_0.3 ;
    %load/vec4 v0x55a5f7e7a990_0;
    %ix/load 4, 70, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55a5f7e7a8d0_0, 4, 32;
    %jmp T_0.5;
T_0.4 ;
    %load/vec4 v0x55a5f7e7a990_0;
    %ix/load 4, 102, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55a5f7e7a8d0_0, 4, 32;
    %jmp T_0.5;
T_0.5 ;
    %pop/vec4 1;
    %load/vec4 v0x55a5f7d89fa0_0;
    %parti/u 2, 4, 32;
    %addi 1, 0, 2;
    %ix/load 4, 4, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55a5f7e7a8d0_0, 4, 2;
    %load/vec4 v0x55a5f7d89fa0_0;
    %parti/u 2, 2, 32;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_0.6, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_0.7, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_0.8, 6;
    %pushi/vec4 1, 0, 1;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55a5f7e7a8d0_0, 4, 1;
    %jmp T_0.10;
T_0.6 ;
    %pushi/vec4 2, 0, 32;
    %load/vec4 v0x55a5f7e7a8d0_0;
    %parti/u 2, 4, 32;
    %pad/u 32;
    %cmp/u;
    %flag_get/vec4 4;
    %flag_get/vec4 5;
    %or;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55a5f7e7a8d0_0, 4, 1;
    %jmp T_0.10;
T_0.7 ;
    %pushi/vec4 3, 0, 32;
    %load/vec4 v0x55a5f7e7a8d0_0;
    %parti/u 2, 4, 32;
    %pad/u 32;
    %cmp/u;
    %flag_get/vec4 4;
    %flag_get/vec4 5;
    %or;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55a5f7e7a8d0_0, 4, 1;
    %jmp T_0.10;
T_0.8 ;
    %pushi/vec4 4, 0, 32;
    %load/vec4 v0x55a5f7e7a8d0_0;
    %parti/u 2, 4, 32;
    %pad/u 32;
    %cmp/u;
    %flag_get/vec4 4;
    %flag_get/vec4 5;
    %or;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55a5f7e7a8d0_0, 4, 1;
    %jmp T_0.10;
T_0.10 ;
    %pop/vec4 1;
T_0.0 ;
    %load/vec4 v0x55a5f7e7a8d0_0;
    %ret/vec4 0, 0, 134;  Assign to add_word_to_collector (store_vec4_to_lval)
    %disable S_0x55a5f7e08160;
    %end;
S_0x55a5f7e7aa70 .scope autofunction.str, "disassemble_instruction" "disassemble_instruction" 3 521, 3 521 0, S_0x55a5f7e029c0;
 .timescale 0 0;
; Variable disassemble_instruction is string return value of scope S_0x55a5f7e7aa70
v0x55a5f7e7ad30_0 .var/str "instr_name";
v0x55a5f7e7adf0_0 .var "instruction_data", 127 0;
v0x55a5f7e7aeb0_0 .var "length", 1 0;
v0x55a5f7e7af90_0 .var/str "operands";
v0x55a5f7e7b0a0_0 .var/str "result";
TD_tile_decoder_pkg.disassemble_instruction ;
    %alloc S_0x55a5f7e7b500;
    %load/vec4 v0x55a5f7e7adf0_0;
    %load/vec4 v0x55a5f7e7aeb0_0;
    %store/vec4 v0x55a5f7e7d5a0_0, 0, 2;
    %store/vec4 v0x55a5f7e7d4e0_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.extract_instruction_name, S_0x55a5f7e7b500;
    %free S_0x55a5f7e7b500;
    %store/str v0x55a5f7e7ad30_0;
    %alloc S_0x55a5f7e7dbd0;
    %load/vec4 v0x55a5f7e7adf0_0;
    %load/vec4 v0x55a5f7e7aeb0_0;
    %load/str v0x55a5f7e7ad30_0;
    %store/str v0x55a5f7e7f830_0;
    %store/vec4 v0x55a5f7e7f9b0_0, 0, 2;
    %store/vec4 v0x55a5f7e7f8f0_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.format_operands, S_0x55a5f7e7dbd0;
    %free S_0x55a5f7e7dbd0;
    %store/str v0x55a5f7e7af90_0;
    %load/str v0x55a5f7e7af90_0;
    %pushi/str "";
    %cmp/str;
    %flag_inv 4;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_1.11, 8;
    %vpi_call/w 3 531 "$sformat", v0x55a5f7e7b0a0_0, "%s %s", v0x55a5f7e7ad30_0, v0x55a5f7e7af90_0 {0 0 0};
    %jmp T_1.12;
T_1.11 ;
    %load/str v0x55a5f7e7ad30_0;
    %store/str v0x55a5f7e7b0a0_0;
T_1.12 ;
    %load/str v0x55a5f7e7b0a0_0;
    %ret/str 0; Assign to disassemble_instruction
    %disable S_0x55a5f7e7aa70;
    %end;
S_0x55a5f7e7b160 .scope autofunction.vec4.s7, "extract_ace_op" "extract_ace_op" 3 28, 3 28 0, S_0x55a5f7e029c0;
 .timescale 0 0;
; Variable extract_ace_op is vec4 return value of scope S_0x55a5f7e7b160
v0x55a5f7e7b420_0 .var "word", 31 0;
TD_tile_decoder_pkg.extract_ace_op ;
    %load/vec4 v0x55a5f7e7b420_0;
    %parti/s 7, 0, 2;
    %ret/vec4 0, 0, 7;  Assign to extract_ace_op (store_vec4_to_lval)
    %disable S_0x55a5f7e7b160;
    %end;
S_0x55a5f7e7b500 .scope autofunction.str, "extract_instruction_name" "extract_instruction_name" 3 174, 3 174 0, S_0x55a5f7e029c0;
 .timescale 0 0;
v0x55a5f7e7d320_0 .var "ace_op", 6 0;
; Variable extract_instruction_name is string return value of scope S_0x55a5f7e7b500
v0x55a5f7e7d4e0_0 .var "instruction_data", 127 0;
v0x55a5f7e7d5a0_0 .var "length", 1 0;
v0x55a5f7e7d680_0 .var "lsuop", 1 0;
v0x55a5f7e7d7b0_0 .var "memuop", 5 0;
v0x55a5f7e7d890_0 .var "miscop", 2 0;
v0x55a5f7e7d970_0 .var "offseten", 0 0;
v0x55a5f7e7da30_0 .var "rmten", 0 0;
v0x55a5f7e7daf0_0 .var "tuop", 2 0;
TD_tile_decoder_pkg.extract_instruction_name ;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0x55a5f7e7d320_0, 0, 7;
    %load/vec4 v0x55a5f7e7d320_0;
    %cmpi/ne 123, 0, 7;
    %jmp/0xz  T_3.13, 4;
    %pushi/str "unknown_non_tile";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
T_3.13 ;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0x55a5f7e7daf0_0, 0, 3;
    %load/vec4 v0x55a5f7e7daf0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_3.15, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 3;
    %cmp/u;
    %jmp/1 T_3.16, 6;
    %dup/vec4;
    %pushi/vec4 6, 0, 3;
    %cmp/u;
    %jmp/1 T_3.17, 6;
    %dup/vec4;
    %pushi/vec4 4, 0, 3;
    %cmp/u;
    %jmp/1 T_3.18, 6;
    %dup/vec4;
    %pushi/vec4 5, 0, 3;
    %cmp/u;
    %jmp/1 T_3.19, 6;
    %dup/vec4;
    %pushi/vec4 7, 0, 3;
    %cmp/u;
    %jmp/1 T_3.20, 6;
    %pushi/str "unknown_tile";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.22;
T_3.15 ;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55a5f7e7d7b0_0, 0, 6;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0x55a5f7e7d680_0, 0, 2;
    %load/vec4 v0x55a5f7e7d7b0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 6;
    %cmp/u;
    %jmp/1 T_3.23, 6;
    %dup/vec4;
    %pushi/vec4 8, 0, 6;
    %cmp/u;
    %jmp/1 T_3.24, 6;
    %dup/vec4;
    %pushi/vec4 28, 0, 6;
    %cmp/u;
    %jmp/1 T_3.25, 6;
    %jmp T_3.26;
T_3.23 ;
    %load/vec4 v0x55a5f7e7d680_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_3.27, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_3.28, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_3.29, 6;
    %jmp T_3.30;
T_3.27 ;
    %load/vec4 v0x55a5f7e7d5a0_0;
    %cmpi/e 1, 0, 2;
    %jmp/0xz  T_3.31, 4;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 1, 47, 7;
    %store/vec4 v0x55a5f7e7d970_0, 0, 1;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 1, 52, 7;
    %store/vec4 v0x55a5f7e7da30_0, 0, 1;
    %load/vec4 v0x55a5f7e7d970_0;
    %nor/r;
    %load/vec4 v0x55a5f7e7da30_0;
    %nor/r;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.33, 8;
    %pushi/str "tld.trii.linear.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.34;
T_3.33 ;
    %load/vec4 v0x55a5f7e7d970_0;
    %nor/r;
    %load/vec4 v0x55a5f7e7da30_0;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.35, 8;
    %pushi/str "tld.trii.linear.u32.global.remote";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.36;
T_3.35 ;
    %load/vec4 v0x55a5f7e7d970_0;
    %load/vec4 v0x55a5f7e7da30_0;
    %nor/r;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.37, 8;
    %pushi/str "tld.trir.linear.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.38;
T_3.37 ;
    %pushi/str "tld.trir.linear.u32.global.remote";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
T_3.38 ;
T_3.36 ;
T_3.34 ;
T_3.31 ;
    %jmp T_3.30;
T_3.28 ;
    %pushi/str "tld.trri.stride.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.30;
T_3.29 ;
    %load/vec4 v0x55a5f7e7d5a0_0;
    %cmpi/e 2, 0, 2;
    %jmp/0xz  T_3.39, 4;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 1, 79, 8;
    %store/vec4 v0x55a5f7e7d970_0, 0, 1;
    %load/vec4 v0x55a5f7e7d970_0;
    %nor/r;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.41, 8;
    %pushi/str "tld.trvi.asp.index.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.42;
T_3.41 ;
    %pushi/str "tld.trvr.asp.index.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
T_3.42 ;
T_3.39 ;
    %jmp T_3.30;
T_3.30 ;
    %pop/vec4 1;
    %jmp T_3.26;
T_3.24 ;
    %pushi/str "tst.trvi.index.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.26;
T_3.25 ;
    %load/vec4 v0x55a5f7e7d5a0_0;
    %cmpi/e 3, 0, 2;
    %jmp/0xz  T_3.43, 4;
    %fork t_1, S_0x55a5f7e7b6e0;
    %jmp t_0;
    .scope S_0x55a5f7e7b6e0;
t_1 ;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 1, 73, 8;
    %store/vec4 v0x55a5f7e7b9c0_0, 0, 1;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 1, 74, 8;
    %store/vec4 v0x55a5f7e7b8e0_0, 0, 1;
    %load/vec4 v0x55a5f7e7b9c0_0;
    %nor/r;
    %load/vec4 v0x55a5f7e7b8e0_0;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.45, 8;
    %pushi/str "tacp.rvv.asp2.dsttm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.46;
T_3.45 ;
    %load/vec4 v0x55a5f7e7b9c0_0;
    %load/vec4 v0x55a5f7e7b8e0_0;
    %nor/r;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.47, 8;
    %pushi/str "tacp.vvr.asp2.srctm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.48;
T_3.47 ;
    %pushi/str "tacp.rvrv.asp2.mbar.dsttm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
T_3.48 ;
T_3.46 ;
    %end;
    .scope S_0x55a5f7e7b500;
t_0 %join;
T_3.43 ;
    %jmp T_3.26;
T_3.26 ;
    %pop/vec4 1;
    %jmp T_3.22;
T_3.16 ;
    %pushi/str "tmma.ttt";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.22;
T_3.17 ;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55a5f7e7d7b0_0, 0, 6;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0x55a5f7e7d680_0, 0, 2;
    %load/vec4 v0x55a5f7e7d5a0_0;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_3.49, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_3.50, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_3.51, 6;
    %pushi/str "ace_low";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.53;
T_3.49 ;
    %fork t_3, S_0x55a5f7e7ba80;
    %jmp t_2;
    .scope S_0x55a5f7e7ba80;
t_3 ;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 3, 44, 7;
    %store/vec4 v0x55a5f7e7bc80_0, 0, 3;
    %load/vec4 v0x55a5f7e7d7b0_0;
    %cmpi/e 1, 0, 6;
    %jmp/0xz  T_3.54, 4;
    %load/vec4 v0x55a5f7e7bc80_0;
    %cmpi/e 1, 0, 3;
    %jmp/0xz  T_3.56, 4;
    %load/vec4 v0x55a5f7e7d680_0;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_3.58, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_3.59, 6;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_3.60, 6;
    %pushi/str "unknown_blk_lsuop";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.62;
T_3.58 ;
    %pushi/str "tld.trr.blk.mx48.share";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.62;
T_3.59 ;
    %pushi/str "tld.trr.blk.mx6.share";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.62;
T_3.60 ;
    %pushi/str "tld.trr.blk.share";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.62;
T_3.62 ;
    %pop/vec4 1;
    %jmp T_3.57;
T_3.56 ;
    %pushi/str "unknown_blk_second_tuop";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
T_3.57 ;
    %jmp T_3.55;
T_3.54 ;
    %load/vec4 v0x55a5f7e7d7b0_0;
    %cmpi/e 0, 0, 6;
    %jmp/0xz  T_3.63, 4;
    %load/vec4 v0x55a5f7e7bc80_0;
    %cmpi/e 0, 0, 3;
    %jmp/0xz  T_3.65, 4;
    %load/vec4 v0x55a5f7e7d680_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_3.67, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_3.68, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_3.69, 6;
    %pushi/str "unknown_std_lsuop";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.71;
T_3.67 ;
    %pushi/str "tld.trii.linear.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.71;
T_3.68 ;
    %pushi/str "tld.trri.stride.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.71;
T_3.69 ;
    %pushi/str "tld.other.64bit";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.71;
T_3.71 ;
    %pop/vec4 1;
    %jmp T_3.66;
T_3.65 ;
    %pushi/str "unknown_std_second_tuop";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
T_3.66 ;
    %jmp T_3.64;
T_3.63 ;
    %pushi/str "ace_low";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
T_3.64 ;
T_3.55 ;
    %end;
    .scope S_0x55a5f7e7b500;
t_2 %join;
    %jmp T_3.53;
T_3.50 ;
    %load/vec4 v0x55a5f7e7d7b0_0;
    %pushi/vec4 0, 0, 6;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x55a5f7e7d680_0;
    %pushi/vec4 2, 0, 2;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.72, 8;
    %fork t_5, S_0x55a5f7e7bd60;
    %jmp t_4;
    .scope S_0x55a5f7e7bd60;
t_5 ;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 3, 44, 7;
    %store/vec4 v0x55a5f7e7c200_0, 0, 3;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 3, 76, 8;
    %store/vec4 v0x55a5f7e7c300_0, 0, 3;
    %load/vec4 v0x55a5f7e7c200_0;
    %pushi/vec4 0, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x55a5f7e7c300_0;
    %pushi/vec4 7, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.74, 8;
    %fork t_7, S_0x55a5f7e7bf40;
    %jmp t_6;
    .scope S_0x55a5f7e7bf40;
t_7 ;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 1, 9, 5;
    %store/vec4 v0x55a5f7e7c120_0, 0, 1;
    %load/vec4 v0x55a5f7e7c120_0;
    %nor/r;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.76, 8;
    %pushi/str "tld.trvi.asp.index.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.77;
T_3.76 ;
    %pushi/str "tld.trvr.asp.index.u32.global";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
T_3.77 ;
    %end;
    .scope S_0x55a5f7e7bd60;
t_6 %join;
    %jmp T_3.75;
T_3.74 ;
    %pushi/str "unknown_96bit_tuop_pattern";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
T_3.75 ;
    %end;
    .scope S_0x55a5f7e7b500;
t_4 %join;
    %jmp T_3.73;
T_3.72 ;
    %pushi/str "unknown_96bit_memuop_lsuop";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
T_3.73 ;
    %jmp T_3.53;
T_3.51 ;
    %load/vec4 v0x55a5f7e7d7b0_0;
    %cmpi/e 28, 0, 6;
    %jmp/0xz  T_3.78, 4;
    %fork t_9, S_0x55a5f7e7c3e0;
    %jmp t_8;
    .scope S_0x55a5f7e7c3e0;
t_9 ;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 3, 44, 7;
    %store/vec4 v0x55a5f7e7ca60_0, 0, 3;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 3, 76, 8;
    %store/vec4 v0x55a5f7e7cb40_0, 0, 3;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 3, 108, 8;
    %store/vec4 v0x55a5f7e7c960_0, 0, 3;
    %load/vec4 v0x55a5f7e7ca60_0;
    %pushi/vec4 0, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x55a5f7e7cb40_0;
    %pushi/vec4 7, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %load/vec4 v0x55a5f7e7c960_0;
    %pushi/vec4 7, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.80, 8;
    %fork t_11, S_0x55a5f7e7c5c0;
    %jmp t_10;
    .scope S_0x55a5f7e7c5c0;
t_11 ;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 1, 73, 8;
    %store/vec4 v0x55a5f7e7c8a0_0, 0, 1;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 1, 74, 8;
    %store/vec4 v0x55a5f7e7c7c0_0, 0, 1;
    %load/vec4 v0x55a5f7e7c8a0_0;
    %nor/r;
    %load/vec4 v0x55a5f7e7c7c0_0;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.82, 8;
    %pushi/str "tacp.rvv.asp2.dsttm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.83;
T_3.82 ;
    %load/vec4 v0x55a5f7e7c8a0_0;
    %load/vec4 v0x55a5f7e7c7c0_0;
    %nor/r;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.84, 8;
    %pushi/str "tacp.vvr.asp2.srctm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.85;
T_3.84 ;
    %pushi/str "tacp.rvrv.asp2.mbar.dsttm";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
T_3.85 ;
T_3.83 ;
    %end;
    .scope S_0x55a5f7e7c3e0;
t_10 %join;
    %jmp T_3.81;
T_3.80 ;
    %pushi/str "unknown_128bit_tuop_pattern";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
T_3.81 ;
    %end;
    .scope S_0x55a5f7e7b500;
t_8 %join;
    %jmp T_3.79;
T_3.78 ;
    %pushi/str "unknown_128bit_memuop";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
T_3.79 ;
    %jmp T_3.53;
T_3.53 ;
    %pop/vec4 1;
    %jmp T_3.22;
T_3.18 ;
    %fork t_13, S_0x55a5f7e7cc30;
    %jmp t_12;
    .scope S_0x55a5f7e7cc30;
t_13 ;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 2, 30, 6;
    %store/vec4 v0x55a5f7e7ce60_0, 0, 2;
    %load/vec4 v0x55a5f7e7ce60_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_3.86, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_3.87, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_3.88, 6;
    %pushi/str "unknown_csr";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.90;
T_3.86 ;
    %pushi/str "tcsrw.i";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.90;
T_3.87 ;
    %pushi/str "tcsrr.r";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.90;
T_3.88 ;
    %pushi/str "tcsrw.r";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.90;
T_3.90 ;
    %pop/vec4 1;
    %end;
    .scope S_0x55a5f7e7b500;
t_12 %join;
    %jmp T_3.22;
T_3.19 ;
    %fork t_15, S_0x55a5f7e7cf60;
    %jmp t_14;
    .scope S_0x55a5f7e7cf60;
t_15 ;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 3, 23, 6;
    %store/vec4 v0x55a5f7e7d140_0, 0, 3;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 3, 28, 6;
    %store/vec4 v0x55a5f7e7d240_0, 0, 3;
    %load/vec4 v0x55a5f7e7d140_0;
    %pushi/vec4 1, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x55a5f7e7d240_0;
    %pushi/vec4 7, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_3.91, 8;
    %load/vec4 v0x55a5f7e7d4e0_0;
    %parti/s 1, 26, 6;
    %cmpi/e 1, 0, 1;
    %jmp/0xz  T_3.93, 4;
    %pushi/str "twait.mem";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.94;
T_3.93 ;
    %pushi/str "twait";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
T_3.94 ;
    %jmp T_3.92;
T_3.91 ;
    %pushi/str "unknown_sync";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
T_3.92 ;
    %end;
    .scope S_0x55a5f7e7b500;
t_14 %join;
    %jmp T_3.22;
T_3.20 ;
    %pushi/str "ace_mem_high";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %jmp T_3.22;
T_3.22 ;
    %pop/vec4 1;
    %pushi/str "unknown";
    %ret/str 0; Assign to extract_instruction_name
    %disable S_0x55a5f7e7b500;
    %end;
S_0x55a5f7e7b6e0 .scope autobegin, "$unm_blk_19" "$unm_blk_19" 3 230, 3 230 0, S_0x55a5f7e7b500;
 .timescale 0 0;
v0x55a5f7e7b8e0_0 .var "dsttm", 0 0;
v0x55a5f7e7b9c0_0 .var "srctm", 0 0;
S_0x55a5f7e7ba80 .scope autobegin, "$unm_blk_21" "$unm_blk_21" 3 253, 3 253 0, S_0x55a5f7e7b500;
 .timescale 0 0;
v0x55a5f7e7bc80_0 .var "second_tuop", 2 0;
S_0x55a5f7e7bd60 .scope autobegin, "$unm_blk_30" "$unm_blk_30" 3 286, 3 286 0, S_0x55a5f7e7b500;
 .timescale 0 0;
v0x55a5f7e7c200_0 .var "second_tuop", 2 0;
v0x55a5f7e7c300_0 .var "third_tuop", 2 0;
S_0x55a5f7e7bf40 .scope autobegin, "$unm_blk_31" "$unm_blk_31" 3 290, 3 290 0, S_0x55a5f7e7bd60;
 .timescale 0 0;
v0x55a5f7e7c120_0 .var "offseten", 0 0;
S_0x55a5f7e7c3e0 .scope autobegin, "$unm_blk_35" "$unm_blk_35" 3 306, 3 306 0, S_0x55a5f7e7b500;
 .timescale 0 0;
v0x55a5f7e7c960_0 .var "fourth_tuop", 2 0;
v0x55a5f7e7ca60_0 .var "second_tuop", 2 0;
v0x55a5f7e7cb40_0 .var "third_tuop", 2 0;
S_0x55a5f7e7c5c0 .scope autobegin, "$unm_blk_36" "$unm_blk_36" 3 311, 3 311 0, S_0x55a5f7e7c3e0;
 .timescale 0 0;
v0x55a5f7e7c7c0_0 .var "dsttm", 0 0;
v0x55a5f7e7c8a0_0 .var "srctm", 0 0;
S_0x55a5f7e7cc30 .scope autobegin, "$unm_blk_39" "$unm_blk_39" 3 333, 3 333 0, S_0x55a5f7e7b500;
 .timescale 0 0;
v0x55a5f7e7ce60_0 .var "rw", 1 0;
S_0x55a5f7e7cf60 .scope autobegin, "$unm_blk_40" "$unm_blk_40" 3 343, 3 343 0, S_0x55a5f7e7b500;
 .timescale 0 0;
v0x55a5f7e7d140_0 .var "ctrluop", 2 0;
v0x55a5f7e7d240_0 .var "waitop", 2 0;
S_0x55a5f7e7dbd0 .scope autofunction.str, "format_operands" "format_operands" 3 370, 3 370 0, S_0x55a5f7e029c0;
 .timescale 0 0;
; Variable format_operands is string return value of scope S_0x55a5f7e7dbd0
v0x55a5f7e7f830_0 .var/str "instr_name";
v0x55a5f7e7f8f0_0 .var "instruction_data", 127 0;
v0x55a5f7e7f9b0_0 .var "length", 1 0;
v0x55a5f7e7fa90_0 .var/str "operands";
v0x55a5f7e7fba0_0 .var "rs1", 4 0;
v0x55a5f7e7fc80_0 .var "rs2", 4 0;
v0x55a5f7e7fd60_0 .var "td", 7 0;
TD_tile_decoder_pkg.format_operands ;
    %pushi/str "";
    %store/str v0x55a5f7e7fa90_0;
    %load/vec4 v0x55a5f7e7f9b0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_4.95, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_4.96, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_4.97, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_4.98, 6;
    %jmp T_4.99;
T_4.95 ;
    %load/str v0x55a5f7e7f830_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 4, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tcsr";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.100, 8;
T_4.100 ;
    %jmp T_4.99;
T_4.96 ;
    %fork t_17, S_0x55a5f7e7ddb0;
    %jmp t_16;
    .scope S_0x55a5f7e7ddb0;
t_17 ;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0x55a5f7e7e0b0_0, 0, 3;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55a5f7e7dfb0_0, 0, 6;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 3, 44, 7;
    %store/vec4 v0x55a5f7e7e190_0, 0, 3;
    %load/vec4 v0x55a5f7e7e0b0_0;
    %pushi/vec4 6, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x55a5f7e7dfb0_0;
    %pushi/vec4 1, 0, 6;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %load/vec4 v0x55a5f7e7e190_0;
    %pushi/vec4 1, 0, 3;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.102, 8;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 8, 55, 7;
    %store/vec4 v0x55a5f7e7fd60_0, 0, 8;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 5, 47, 7;
    %store/vec4 v0x55a5f7e7fba0_0, 0, 5;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 5, 20, 6;
    %store/vec4 v0x55a5f7e7fc80_0, 0, 5;
    %jmp T_4.103;
T_4.102 ;
    %load/str v0x55a5f7e7f830_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 3, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tld";
    %cmp/str;
    %flag_get/vec4 4;
    %load/str v0x55a5f7e7f830_0;
    %ix/load 4, 4, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 3, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "trr";
    %cmp/str;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.104, 8;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 8, 55, 7;
    %store/vec4 v0x55a5f7e7fd60_0, 0, 8;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 5, 47, 7;
    %store/vec4 v0x55a5f7e7fba0_0, 0, 5;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 5, 20, 6;
    %store/vec4 v0x55a5f7e7fc80_0, 0, 5;
    %jmp T_4.105;
T_4.104 ;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 8, 32, 7;
    %store/vec4 v0x55a5f7e7fd60_0, 0, 8;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 5, 48, 7;
    %store/vec4 v0x55a5f7e7fba0_0, 0, 5;
T_4.105 ;
T_4.103 ;
    %end;
    .scope S_0x55a5f7e7dbd0;
t_16 %join;
    %jmp T_4.99;
T_4.97 ;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 8, 48, 7;
    %store/vec4 v0x55a5f7e7fd60_0, 0, 8;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 5, 43, 7;
    %store/vec4 v0x55a5f7e7fba0_0, 0, 5;
    %jmp T_4.99;
T_4.98 ;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 5, 47, 7;
    %store/vec4 v0x55a5f7e7fba0_0, 0, 5;
    %jmp T_4.99;
T_4.99 ;
    %pop/vec4 1;
    %load/vec4 v0x55a5f7e7f9b0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_4.106, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_4.107, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_4.108, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_4.109, 6;
    %jmp T_4.110;
T_4.106 ;
    %load/str v0x55a5f7e7f830_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 4, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tcsr";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.111, 8;
    %fork t_19, S_0x55a5f7e7e250;
    %jmp t_18;
    .scope S_0x55a5f7e7e250;
t_19 ;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 2, 30, 6;
    %store/vec4 v0x55a5f7e7e6d0_0, 0, 2;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 9, 20, 6;
    %store/vec4 v0x55a5f7e7e450_0, 0, 9;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 5, 7, 4;
    %store/vec4 v0x55a5f7e7e530_0, 0, 5;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x55a5f7e7e610_0, 0, 5;
    %load/vec4 v0x55a5f7e7e6d0_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_4.113, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_4.114, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_4.115, 6;
    %pushi/str "unknown";
    %store/str v0x55a5f7e7fa90_0;
    %jmp T_4.117;
T_4.113 ;
    %vpi_call/w 3 439 "$sformat", v0x55a5f7e7fa90_0, "0x%0x", v0x55a5f7e7e610_0 {0 0 0};
    %jmp T_4.117;
T_4.114 ;
    %vpi_call/w 3 442 "$sformat", v0x55a5f7e7fa90_0, "x%0d", v0x55a5f7e7e530_0 {0 0 0};
    %jmp T_4.117;
T_4.115 ;
    %vpi_call/w 3 445 "$sformat", v0x55a5f7e7fa90_0, "x%0d", v0x55a5f7e7e610_0 {0 0 0};
    %jmp T_4.117;
T_4.117 ;
    %pop/vec4 1;
    %end;
    .scope S_0x55a5f7e7dbd0;
t_18 %join;
    %jmp T_4.112;
T_4.111 ;
    %load/str v0x55a5f7e7f830_0;
    %pushi/str "twait";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %load/str v0x55a5f7e7f830_0;
    %pushi/str "twait.mem";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %jmp/0xz  T_4.118, 9;
    %pushi/str "";
    %store/str v0x55a5f7e7fa90_0;
    %jmp T_4.119;
T_4.118 ;
    %load/str v0x55a5f7e7f830_0;
    %pushi/str "twait.i.load.global";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %load/str v0x55a5f7e7f830_0;
    %pushi/str "twait.i.load.share";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %load/str v0x55a5f7e7f830_0;
    %pushi/str "twait.i.store.global";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %flag_or 8, 9;
    %load/str v0x55a5f7e7f830_0;
    %pushi/str "twait.i.store.share";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %jmp/0xz  T_4.120, 9;
    %fork t_21, S_0x55a5f7e7e7b0;
    %jmp t_20;
    .scope S_0x55a5f7e7e7b0;
t_21 ;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 8, 15, 5;
    %store/vec4 v0x55a5f7e7e9c0_0, 0, 8;
    %vpi_call/w 3 456 "$sformat", v0x55a5f7e7fa90_0, "%0d", v0x55a5f7e7e9c0_0 {0 0 0};
    %end;
    .scope S_0x55a5f7e7dbd0;
t_20 %join;
    %jmp T_4.121;
T_4.120 ;
    %load/str v0x55a5f7e7f830_0;
    %pushi/str "twait.r.load.global";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %load/str v0x55a5f7e7f830_0;
    %pushi/str "twait.r.load.share";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %load/str v0x55a5f7e7f830_0;
    %pushi/str "twait.r.store.global";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %flag_or 8, 9;
    %load/str v0x55a5f7e7f830_0;
    %pushi/str "twait.r.store.share";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %load/str v0x55a5f7e7f830_0;
    %pushi/str "twait.r.tacp_cg";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %flag_or 8, 9;
    %load/str v0x55a5f7e7f830_0;
    %pushi/str "twait.r.rmtfence";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %jmp/0xz  T_4.122, 9;
    %fork t_23, S_0x55a5f7e7eaa0;
    %jmp t_22;
    .scope S_0x55a5f7e7eaa0;
t_23 ;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x55a5f7e7ec80_0, 0, 5;
    %vpi_call/w 3 462 "$sformat", v0x55a5f7e7fa90_0, "x%0d", v0x55a5f7e7ec80_0 {0 0 0};
    %end;
    .scope S_0x55a5f7e7dbd0;
t_22 %join;
    %jmp T_4.123;
T_4.122 ;
    %load/str v0x55a5f7e7f830_0;
    %pushi/str "tsync.i";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.124, 8;
    %fork t_25, S_0x55a5f7e7ed80;
    %jmp t_24;
    .scope S_0x55a5f7e7ed80;
t_25 ;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x55a5f7e7efb0_0, 0, 5;
    %vpi_call/w 3 466 "$sformat", v0x55a5f7e7fa90_0, "%0d", v0x55a5f7e7efb0_0 {0 0 0};
    %end;
    .scope S_0x55a5f7e7dbd0;
t_24 %join;
    %jmp T_4.125;
T_4.124 ;
    %load/str v0x55a5f7e7f830_0;
    %pushi/str "tkill.r";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.126, 8;
    %fork t_27, S_0x55a5f7e7f0b0;
    %jmp t_26;
    .scope S_0x55a5f7e7f0b0;
t_27 ;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 5, 15, 5;
    %store/vec4 v0x55a5f7e7f290_0, 0, 5;
    %vpi_call/w 3 470 "$sformat", v0x55a5f7e7fa90_0, "x%0d", v0x55a5f7e7f290_0 {0 0 0};
    %end;
    .scope S_0x55a5f7e7dbd0;
t_26 %join;
    %jmp T_4.127;
T_4.126 ;
    %load/str v0x55a5f7e7f830_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 3, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "ace";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.128, 8;
    %pushi/str "0, 0, 0, 0, 0";
    %store/str v0x55a5f7e7fa90_0;
    %jmp T_4.129;
T_4.128 ;
    %pushi/str "0";
    %store/str v0x55a5f7e7fa90_0;
T_4.129 ;
T_4.127 ;
T_4.125 ;
T_4.123 ;
T_4.121 ;
T_4.119 ;
T_4.112 ;
    %jmp T_4.110;
T_4.107 ;
    %load/str v0x55a5f7e7f830_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 2, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tld";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %load/str v0x55a5f7e7f830_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 2, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tst";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 9;
    %flag_or 9, 8;
    %jmp/0xz  T_4.130, 9;
    %load/str v0x55a5f7e7f830_0;
    %ix/load 4, 4, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 3, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "trr";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.132, 8;
    %vpi_call/w 3 484 "$sformat", v0x55a5f7e7fa90_0, "t%0d, (x%0d), x%0d", v0x55a5f7e7fd60_0, v0x55a5f7e7fba0_0, v0x55a5f7e7fc80_0 {0 0 0};
    %jmp T_4.133;
T_4.132 ;
    %vpi_call/w 3 487 "$sformat", v0x55a5f7e7fa90_0, "t%0d, (x%0d)", v0x55a5f7e7fd60_0, v0x55a5f7e7fba0_0 {0 0 0};
T_4.133 ;
    %jmp T_4.131;
T_4.130 ;
    %load/str v0x55a5f7e7f830_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 3, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tmma";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.134, 8;
    %fork t_29, S_0x55a5f7e7f390;
    %jmp t_28;
    .scope S_0x55a5f7e7f390;
t_29 ;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 8, 40, 7;
    %store/vec4 v0x55a5f7e7f570_0, 0, 8;
    %load/vec4 v0x55a5f7e7f8f0_0;
    %parti/s 8, 48, 7;
    %store/vec4 v0x55a5f7e7f670_0, 0, 8;
    %vpi_call/w 3 494 "$sformat", v0x55a5f7e7fa90_0, "t%0d, t%0d, t%0d", v0x55a5f7e7fd60_0, v0x55a5f7e7f570_0, v0x55a5f7e7f670_0 {0 0 0};
    %end;
    .scope S_0x55a5f7e7dbd0;
t_28 %join;
    %jmp T_4.135;
T_4.134 ;
    %vpi_call/w 3 497 "$sformat", v0x55a5f7e7fa90_0, "t%0d, (x%0d)", v0x55a5f7e7fd60_0, v0x55a5f7e7fba0_0 {0 0 0};
T_4.135 ;
T_4.131 ;
    %jmp T_4.110;
T_4.108 ;
    %vpi_call/w 3 503 "$sformat", v0x55a5f7e7fa90_0, "t%0d, (x%0d)", v0x55a5f7e7fd60_0, v0x55a5f7e7fba0_0 {0 0 0};
    %jmp T_4.110;
T_4.109 ;
    %load/str v0x55a5f7e7f830_0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %ix/load 5, 3, 0;
    %flag_set/imm 4, 0;
    %substr 4, 5;
    %pushi/str "tacp";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_4.136, 8;
    %vpi_call/w 3 509 "$sformat", v0x55a5f7e7fa90_0, "0, 0, x%0d, 0, 0, 0, 0", v0x55a5f7e7fba0_0 {0 0 0};
    %jmp T_4.137;
T_4.136 ;
    %pushi/str "0, 0, 0, 0";
    %store/str v0x55a5f7e7fa90_0;
T_4.137 ;
    %jmp T_4.110;
T_4.110 ;
    %pop/vec4 1;
    %load/str v0x55a5f7e7fa90_0;
    %ret/str 0; Assign to format_operands
    %disable S_0x55a5f7e7dbd0;
    %end;
S_0x55a5f7e7ddb0 .scope autobegin, "$unm_blk_44" "$unm_blk_44" 3 389, 3 389 0, S_0x55a5f7e7dbd0;
 .timescale 0 0;
v0x55a5f7e7dfb0_0 .var "memuop_field", 5 0;
v0x55a5f7e7e0b0_0 .var "tuop_first", 2 0;
v0x55a5f7e7e190_0 .var "tuop_second", 2 0;
S_0x55a5f7e7e250 .scope autobegin, "$unm_blk_51" "$unm_blk_51" 3 430, 3 430 0, S_0x55a5f7e7dbd0;
 .timescale 0 0;
v0x55a5f7e7e450_0 .var "csr_addr", 8 0;
v0x55a5f7e7e530_0 .var "rd", 4 0;
v0x55a5f7e7e610_0 .var "rs1_or_imm", 4 0;
v0x55a5f7e7e6d0_0 .var "rw", 1 0;
S_0x55a5f7e7e7b0 .scope autobegin, "$unm_blk_56" "$unm_blk_56" 3 453, 3 453 0, S_0x55a5f7e7dbd0;
 .timescale 0 0;
v0x55a5f7e7e9c0_0 .var "cnt", 7 0;
S_0x55a5f7e7eaa0 .scope autobegin, "$unm_blk_57" "$unm_blk_57" 3 459, 3 459 0, S_0x55a5f7e7dbd0;
 .timescale 0 0;
v0x55a5f7e7ec80_0 .var "rs1", 4 0;
S_0x55a5f7e7ed80 .scope autobegin, "$unm_blk_58" "$unm_blk_58" 3 463, 3 463 0, S_0x55a5f7e7dbd0;
 .timescale 0 0;
v0x55a5f7e7efb0_0 .var "sync_id", 4 0;
S_0x55a5f7e7f0b0 .scope autobegin, "$unm_blk_59" "$unm_blk_59" 3 467, 3 467 0, S_0x55a5f7e7dbd0;
 .timescale 0 0;
v0x55a5f7e7f290_0 .var "rs1", 4 0;
S_0x55a5f7e7f390 .scope autobegin, "$unm_blk_66" "$unm_blk_66" 3 489, 3 489 0, S_0x55a5f7e7dbd0;
 .timescale 0 0;
v0x55a5f7e7f570_0 .var "ts1", 7 0;
v0x55a5f7e7f670_0 .var "ts2", 7 0;
S_0x55a5f7e7fe40 .scope autofunction.vec2.u32, "get_instruction_bits" "get_instruction_bits" 3 163, 3 163 0, S_0x55a5f7e029c0;
 .timescale 0 0;
; Variable get_instruction_bits is bool return value of scope S_0x55a5f7e7fe40
v0x55a5f7e80120_0 .var "length", 1 0;
TD_tile_decoder_pkg.get_instruction_bits ;
    %load/vec4 v0x55a5f7e80120_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_5.138, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_5.139, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_5.140, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_5.141, 6;
    %pushi/vec4 32, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x55a5f7e7fe40;
    %jmp T_5.143;
T_5.138 ;
    %pushi/vec4 32, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x55a5f7e7fe40;
    %jmp T_5.143;
T_5.139 ;
    %pushi/vec4 64, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x55a5f7e7fe40;
    %jmp T_5.143;
T_5.140 ;
    %pushi/vec4 96, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x55a5f7e7fe40;
    %jmp T_5.143;
T_5.141 ;
    %pushi/vec4 128, 0, 32;
    %ret/vec4 0, 0, 32;  Assign to get_instruction_bits (store_vec4_to_lval)
    %disable S_0x55a5f7e7fe40;
    %jmp T_5.143;
T_5.143 ;
    %pop/vec4 1;
    %end;
S_0x55a5f7e80200 .scope autofunction.vec4.s2, "get_instruction_length" "get_instruction_length" 3 40, 3 40 0, S_0x55a5f7e029c0;
 .timescale 0 0;
v0x55a5f7e803e0_0 .var "ace_op", 6 0;
v0x55a5f7e804e0_0 .var "first_word", 31 0;
; Variable get_instruction_length is vec4 return value of scope S_0x55a5f7e80200
v0x55a5f7e80680_0 .var "lsuop", 1 0;
v0x55a5f7e80760_0 .var "memuop", 5 0;
v0x55a5f7e80890_0 .var "tuop", 2 0;
TD_tile_decoder_pkg.get_instruction_length ;
    %alloc S_0x55a5f7e7b160;
    %load/vec4 v0x55a5f7e804e0_0;
    %store/vec4 v0x55a5f7e7b420_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.extract_ace_op, S_0x55a5f7e7b160;
    %free S_0x55a5f7e7b160;
    %store/vec4 v0x55a5f7e803e0_0, 0, 7;
    %load/vec4 v0x55a5f7e803e0_0;
    %cmpi/ne 123, 0, 7;
    %jmp/0xz  T_6.144, 4;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
T_6.144 ;
    %load/vec4 v0x55a5f7e804e0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0x55a5f7e80890_0, 0, 3;
    %load/vec4 v0x55a5f7e80890_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 3;
    %cmp/u;
    %jmp/1 T_6.146, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 3;
    %cmp/u;
    %jmp/1 T_6.147, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 3;
    %cmp/u;
    %jmp/1 T_6.148, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 3;
    %cmp/u;
    %jmp/1 T_6.149, 6;
    %dup/vec4;
    %pushi/vec4 4, 0, 3;
    %cmp/u;
    %jmp/1 T_6.150, 6;
    %dup/vec4;
    %pushi/vec4 5, 0, 3;
    %cmp/u;
    %jmp/1 T_6.151, 6;
    %dup/vec4;
    %pushi/vec4 6, 0, 3;
    %cmp/u;
    %jmp/1 T_6.152, 6;
    %dup/vec4;
    %pushi/vec4 7, 0, 3;
    %cmp/u;
    %jmp/1 T_6.153, 6;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.155;
T_6.146 ;
    %load/vec4 v0x55a5f7e804e0_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55a5f7e80760_0, 0, 6;
    %load/vec4 v0x55a5f7e804e0_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0x55a5f7e80680_0, 0, 2;
    %load/vec4 v0x55a5f7e80680_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_6.156, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_6.157, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_6.158, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_6.159, 6;
    %jmp T_6.160;
T_6.156 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.160;
T_6.157 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.160;
T_6.158 ;
    %pushi/vec4 2, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.160;
T_6.159 ;
    %load/vec4 v0x55a5f7e80760_0;
    %cmpi/e 28, 0, 6;
    %jmp/0xz  T_6.161, 4;
    %pushi/vec4 3, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.162;
T_6.161 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
T_6.162 ;
    %jmp T_6.160;
T_6.160 ;
    %pop/vec4 1;
    %jmp T_6.155;
T_6.147 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.155;
T_6.148 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.155;
T_6.149 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.155;
T_6.150 ;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.155;
T_6.151 ;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.155;
T_6.152 ;
    %load/vec4 v0x55a5f7e804e0_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0x55a5f7e80680_0, 0, 2;
    %load/vec4 v0x55a5f7e804e0_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55a5f7e80760_0, 0, 6;
    %load/vec4 v0x55a5f7e80760_0;
    %cmpi/e 1, 0, 6;
    %jmp/0xz  T_6.163, 4;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.164;
T_6.163 ;
    %load/vec4 v0x55a5f7e80760_0;
    %cmpi/e 0, 0, 6;
    %jmp/0xz  T_6.165, 4;
    %load/vec4 v0x55a5f7e80680_0;
    %dup/vec4;
    %pushi/vec4 0, 0, 2;
    %cmp/u;
    %jmp/1 T_6.167, 6;
    %dup/vec4;
    %pushi/vec4 1, 0, 2;
    %cmp/u;
    %jmp/1 T_6.168, 6;
    %dup/vec4;
    %pushi/vec4 2, 0, 2;
    %cmp/u;
    %jmp/1 T_6.169, 6;
    %dup/vec4;
    %pushi/vec4 3, 0, 2;
    %cmp/u;
    %jmp/1 T_6.170, 6;
    %jmp T_6.171;
T_6.167 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.171;
T_6.168 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.171;
T_6.169 ;
    %pushi/vec4 2, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.171;
T_6.170 ;
    %pushi/vec4 1, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.171;
T_6.171 ;
    %pop/vec4 1;
    %jmp T_6.166;
T_6.165 ;
    %load/vec4 v0x55a5f7e80760_0;
    %cmpi/e 28, 0, 6;
    %jmp/0xz  T_6.172, 4;
    %pushi/vec4 3, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.173;
T_6.172 ;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
T_6.173 ;
T_6.166 ;
T_6.164 ;
    %jmp T_6.155;
T_6.153 ;
    %load/vec4 v0x55a5f7e804e0_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x55a5f7e80760_0, 0, 6;
    %pushi/vec4 0, 0, 2;
    %ret/vec4 0, 0, 2;  Assign to get_instruction_length (store_vec4_to_lval)
    %disable S_0x55a5f7e80200;
    %jmp T_6.155;
T_6.155 ;
    %pop/vec4 1;
    %end;
S_0x55a5f7e80970 .scope autofunction.vec4.s134, "init_collector" "init_collector" 3 117, 3 117 0, S_0x55a5f7e029c0;
 .timescale 0 0;
v0x55a5f7e80b50_0 .var "collector", 133 0;
v0x55a5f7e80c50_0 .var "first_word", 31 0;
; Variable init_collector is vec4 return value of scope S_0x55a5f7e80970
TD_tile_decoder_pkg.init_collector ;
    %pushi/vec4 0, 0, 128;
    %ix/load 4, 6, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55a5f7e80b50_0, 4, 128;
    %load/vec4 v0x55a5f7e80c50_0;
    %ix/load 4, 6, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55a5f7e80b50_0, 4, 32;
    %pushi/vec4 1, 0, 2;
    %ix/load 4, 4, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55a5f7e80b50_0, 4, 2;
    %alloc S_0x55a5f7e80200;
    %load/vec4 v0x55a5f7e80c50_0;
    %store/vec4 v0x55a5f7e804e0_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.get_instruction_length, S_0x55a5f7e80200;
    %free S_0x55a5f7e80200;
    %ix/load 4, 2, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55a5f7e80b50_0, 4, 2;
    %alloc S_0x55a5f7e80df0;
    %load/vec4 v0x55a5f7e80c50_0;
    %store/vec4 v0x55a5f7e81160_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.is_tile_instruction, S_0x55a5f7e80df0;
    %free S_0x55a5f7e80df0;
    %ix/load 4, 0, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55a5f7e80b50_0, 4, 1;
    %load/vec4 v0x55a5f7e80b50_0;
    %parti/u 2, 2, 32;
    %pushi/vec4 0, 0, 2;
    %cmp/e;
    %flag_get/vec4 4;
    %ix/load 4, 1, 0;
    %flag_set/imm 4, 0;
    %store/vec4 v0x55a5f7e80b50_0, 4, 1;
    %load/vec4 v0x55a5f7e80b50_0;
    %ret/vec4 0, 0, 134;  Assign to init_collector (store_vec4_to_lval)
    %disable S_0x55a5f7e80970;
    %end;
S_0x55a5f7e80df0 .scope autofunction.vec4.s1, "is_tile_instruction" "is_tile_instruction" 3 33, 3 33 0, S_0x55a5f7e029c0;
 .timescale 0 0;
v0x55a5f7e81060_0 .var "ace_op", 6 0;
v0x55a5f7e81160_0 .var "first_word", 31 0;
; Variable is_tile_instruction is vec4 return value of scope S_0x55a5f7e80df0
TD_tile_decoder_pkg.is_tile_instruction ;
    %alloc S_0x55a5f7e7b160;
    %load/vec4 v0x55a5f7e81160_0;
    %store/vec4 v0x55a5f7e7b420_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.extract_ace_op, S_0x55a5f7e7b160;
    %free S_0x55a5f7e7b160;
    %store/vec4 v0x55a5f7e81060_0, 0, 7;
    %load/vec4 v0x55a5f7e81060_0;
    %pushi/vec4 123, 0, 7;
    %cmp/e;
    %flag_get/vec4 4;
    %ret/vec4 0, 0, 1;  Assign to is_tile_instruction (store_vec4_to_lval)
    %disable S_0x55a5f7e80df0;
    %end;
S_0x55a5f7e07e40 .scope module, "final_test" "final_test" 4 4;
 .timescale 0 0;
S_0x55a5f7e81310 .scope begin, "$unm_blk_77" "$unm_blk_77" 4 6, 4 6 0, S_0x55a5f7e07e40;
 .timescale 0 0;
v0x55a5f7e814c0_0 .var "collector", 133 0;
v0x55a5f7e815c0_0 .var/str "disasm_result";
v0x55a5f7e81680_0 .var "test_instruction", 31 0;
S_0x55a5f7e07fd0 .scope module, "tile_instruction_decoder_example" "tile_instruction_decoder_example" 3 543;
 .timescale 0 0;
S_0x55a5f7e81770 .scope begin, "$unm_blk_72" "$unm_blk_72" 3 546, 3 546 0, S_0x55a5f7e07fd0;
 .timescale 0 0;
v0x55a5f7e81970_0 .var "collector", 133 0;
v0x55a5f7e81a70_0 .var/str "disasm_result";
v0x55a5f7e81b30_0 .var "test_word", 31 0;
    .scope S_0x55a5f7e07e40;
T_9 ;
    %fork t_31, S_0x55a5f7e81310;
    %jmp t_30;
    .scope S_0x55a5f7e81310;
t_31 ;
    %vpi_call/w 4 11 "$display", "=== Final twait instruction fix verification ===" {0 0 0};
    %pushi/vec4 1887457403, 0, 32;
    %store/vec4 v0x55a5f7e81680_0, 0, 32;
    %vpi_call/w 4 15 "$display", "\012Testing instruction: 0x%08x", v0x55a5f7e81680_0 {0 0 0};
    %alloc S_0x55a5f7e80df0;
    %load/vec4 v0x55a5f7e81680_0;
    %store/vec4 v0x55a5f7e81160_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.is_tile_instruction, S_0x55a5f7e80df0;
    %free S_0x55a5f7e80df0;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.0, 8;
    %vpi_call/w 4 18 "$display", "\342\234\223 Recognized as tile instruction" {0 0 0};
    %alloc S_0x55a5f7e80970;
    %load/vec4 v0x55a5f7e81680_0;
    %store/vec4 v0x55a5f7e80c50_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x55a5f7e80970;
    %free S_0x55a5f7e80970;
    %store/vec4 v0x55a5f7e814c0_0, 0, 134;
    %vpi_call/w 4 21 "$display", "\342\234\223 Expected length: %0d (32-bit)", &PV<v0x55a5f7e814c0_0, 2, 2> {0 0 0};
    %load/vec4 v0x55a5f7e814c0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_9.2, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_9.3, 8;
T_9.2 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_9.3, 8;
 ; End of false expr.
    %blend;
T_9.3;
    %vpi_call/w 4 22 "$display", "\342\234\223 Is complete: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x55a5f7e814c0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.4, 8;
    %alloc S_0x55a5f7e7aa70;
    %load/vec4 v0x55a5f7e814c0_0;
    %parti/u 128, 6, 32;
    %load/vec4 v0x55a5f7e814c0_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x55a5f7e7aeb0_0, 0, 2;
    %store/vec4 v0x55a5f7e7adf0_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x55a5f7e7aa70;
    %free S_0x55a5f7e7aa70;
    %store/str v0x55a5f7e815c0_0;
    %vpi_call/w 4 27 "$display", "\342\234\223 Disassembly: %s", v0x55a5f7e815c0_0 {0 0 0};
    %load/str v0x55a5f7e815c0_0;
    %pushi/str "twait";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.6, 8;
    %vpi_call/w 4 30 "$display", "\342\234\223 SUCCESS: Instruction 0x7080507B correctly identified as 'twait'" {0 0 0};
    %vpi_call/w 4 31 "$display", "\342\234\223 FIXED: Previously returned 'unknown_tile', now returns 'twait'" {0 0 0};
    %jmp T_9.7;
T_9.6 ;
    %vpi_call/w 4 33 "$display", "\342\234\227 FAILURE: Expected 'twait', got '%s'", v0x55a5f7e815c0_0 {0 0 0};
T_9.7 ;
    %jmp T_9.5;
T_9.4 ;
    %vpi_call/w 4 36 "$display", "\342\234\227 FAILURE: Instruction should be complete for 32-bit" {0 0 0};
T_9.5 ;
    %jmp T_9.1;
T_9.0 ;
    %vpi_call/w 4 39 "$display", "\342\234\227 FAILURE: Not recognized as tile instruction" {0 0 0};
T_9.1 ;
    %vpi_call/w 4 43 "$display", "\012=== Testing other instructions to ensure no regression ===" {0 0 0};
    %pushi/vec4 16507, 0, 32;
    %store/vec4 v0x55a5f7e81680_0, 0, 32;
    %vpi_call/w 4 47 "$display", "\012Testing CSR instruction: 0x%08x", v0x55a5f7e81680_0 {0 0 0};
    %alloc S_0x55a5f7e80970;
    %load/vec4 v0x55a5f7e81680_0;
    %store/vec4 v0x55a5f7e80c50_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x55a5f7e80970;
    %free S_0x55a5f7e80970;
    %store/vec4 v0x55a5f7e814c0_0, 0, 134;
    %load/vec4 v0x55a5f7e814c0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.8, 8;
    %alloc S_0x55a5f7e7aa70;
    %load/vec4 v0x55a5f7e814c0_0;
    %parti/u 128, 6, 32;
    %load/vec4 v0x55a5f7e814c0_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x55a5f7e7aeb0_0, 0, 2;
    %store/vec4 v0x55a5f7e7adf0_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x55a5f7e7aa70;
    %free S_0x55a5f7e7aa70;
    %store/str v0x55a5f7e815c0_0;
    %vpi_call/w 4 52 "$display", "  Result: %s", v0x55a5f7e815c0_0 {0 0 0};
    %load/str v0x55a5f7e815c0_0;
    %pushi/str "tcsrw.i 0";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.10, 8;
    %vpi_call/w 4 54 "$display", "  \342\234\223 CSR instruction still works correctly" {0 0 0};
    %jmp T_9.11;
T_9.10 ;
    %vpi_call/w 4 56 "$display", "  \342\234\227 CSR instruction broken" {0 0 0};
T_9.11 ;
T_9.8 ;
    %pushi/vec4 1954566267, 0, 32;
    %store/vec4 v0x55a5f7e81680_0, 0, 32;
    %vpi_call/w 4 62 "$display", "\012Testing twait.mem instruction: 0x%08x", v0x55a5f7e81680_0 {0 0 0};
    %alloc S_0x55a5f7e80970;
    %load/vec4 v0x55a5f7e81680_0;
    %store/vec4 v0x55a5f7e80c50_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x55a5f7e80970;
    %free S_0x55a5f7e80970;
    %store/vec4 v0x55a5f7e814c0_0, 0, 134;
    %load/vec4 v0x55a5f7e814c0_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.12, 8;
    %alloc S_0x55a5f7e7aa70;
    %load/vec4 v0x55a5f7e814c0_0;
    %parti/u 128, 6, 32;
    %load/vec4 v0x55a5f7e814c0_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x55a5f7e7aeb0_0, 0, 2;
    %store/vec4 v0x55a5f7e7adf0_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x55a5f7e7aa70;
    %free S_0x55a5f7e7aa70;
    %store/str v0x55a5f7e815c0_0;
    %vpi_call/w 4 67 "$display", "  Result: %s", v0x55a5f7e815c0_0 {0 0 0};
    %load/str v0x55a5f7e815c0_0;
    %pushi/str "twait.mem";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_9.14, 8;
    %vpi_call/w 4 69 "$display", "  \342\234\223 twait.mem variant works correctly" {0 0 0};
    %jmp T_9.15;
T_9.14 ;
    %vpi_call/w 4 71 "$display", "  \342\234\227 twait.mem variant not working as expected" {0 0 0};
T_9.15 ;
T_9.12 ;
    %vpi_call/w 4 75 "$display", "\012=== Summary ===" {0 0 0};
    %vpi_call/w 4 76 "$display", "\342\234\223 Root cause identified: Missing tuop_101 (sync operations) support" {0 0 0};
    %vpi_call/w 4 77 "$display", "\342\234\223 Fix implemented: Added tuop_101 case in extract_instruction_name function" {0 0 0};
    %vpi_call/w 4 78 "$display", "\342\234\223 Instruction 0x7080507B now correctly identified as 'twait'" {0 0 0};
    %vpi_call/w 4 79 "$display", "\342\234\223 Field extraction corrected: waitop field is [30:28], not [28:26]" {0 0 0};
    %vpi_call/w 4 80 "$display", "\342\234\223 No regression in existing functionality" {0 0 0};
    %vpi_call/w 4 82 "$display", "\012=== Test Complete ===" {0 0 0};
    %end;
    .scope S_0x55a5f7e07e40;
t_30 %join;
    %end;
    .thread T_9;
    .scope S_0x55a5f7e07fd0;
T_10 ;
    %fork t_33, S_0x55a5f7e81770;
    %jmp t_32;
    .scope S_0x55a5f7e81770;
t_33 ;
    %vpi_call/w 3 551 "$display", "=== Tile Instruction Decoder Example ===" {0 0 0};
    %pushi/vec4 16507, 0, 32;
    %store/vec4 v0x55a5f7e81b30_0, 0, 32;
    %vpi_call/w 3 555 "$display", "\012Testing 32-bit instruction: 0x%08x", v0x55a5f7e81b30_0 {0 0 0};
    %alloc S_0x55a5f7e80df0;
    %load/vec4 v0x55a5f7e81b30_0;
    %store/vec4 v0x55a5f7e81160_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.is_tile_instruction, S_0x55a5f7e80df0;
    %free S_0x55a5f7e80df0;
    %flag_set/vec4 8;
    %jmp/0xz  T_10.0, 8;
    %alloc S_0x55a5f7e80970;
    %load/vec4 v0x55a5f7e81b30_0;
    %store/vec4 v0x55a5f7e80c50_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x55a5f7e80970;
    %free S_0x55a5f7e80970;
    %store/vec4 v0x55a5f7e81970_0, 0, 134;
    %load/vec4 v0x55a5f7e81970_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_10.2, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_10.3, 8;
T_10.2 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_10.3, 8;
 ; End of false expr.
    %blend;
T_10.3;
    %vpi_call/w 3 559 "$display", "  Is tile: YES, Length: %0d, Complete: %s", &PV<v0x55a5f7e81970_0, 2, 2>, S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x55a5f7e81970_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_10.4, 8;
    %alloc S_0x55a5f7e7aa70;
    %load/vec4 v0x55a5f7e81970_0;
    %parti/u 128, 6, 32;
    %load/vec4 v0x55a5f7e81970_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x55a5f7e7aeb0_0, 0, 2;
    %store/vec4 v0x55a5f7e7adf0_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x55a5f7e7aa70;
    %free S_0x55a5f7e7aa70;
    %store/str v0x55a5f7e81a70_0;
    %vpi_call/w 3 566 "$display", "  Disassembly: %s", v0x55a5f7e81a70_0 {0 0 0};
T_10.4 ;
T_10.0 ;
    %vpi_call/w 3 571 "$display", "\012Testing 64-bit instruction:" {0 0 0};
    %pushi/vec4 24699, 0, 32;
    %store/vec4 v0x55a5f7e81b30_0, 0, 32;
    %vpi_call/w 3 573 "$display", "  Word 1: 0x%08x", v0x55a5f7e81b30_0 {0 0 0};
    %alloc S_0x55a5f7e80970;
    %load/vec4 v0x55a5f7e81b30_0;
    %store/vec4 v0x55a5f7e80c50_0, 0, 32;
    %callf/vec4 TD_tile_decoder_pkg.init_collector, S_0x55a5f7e80970;
    %free S_0x55a5f7e80970;
    %store/vec4 v0x55a5f7e81970_0, 0, 134;
    %load/vec4 v0x55a5f7e81970_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_10.6, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_10.7, 8;
T_10.6 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_10.7, 8;
 ; End of false expr.
    %blend;
T_10.7;
    %vpi_call/w 3 576 "$display", "  Expected length: %0d, Complete: %s", &PV<v0x55a5f7e81970_0, 2, 2>, S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x55a5f7e81970_0;
    %parti/u 1, 1, 32;
    %nor/r;
    %flag_set/vec4 8;
    %jmp/0xz  T_10.8, 8;
    %pushi/vec4 2147483771, 0, 32;
    %store/vec4 v0x55a5f7e81b30_0, 0, 32;
    %vpi_call/w 3 582 "$display", "  Word 2: 0x%08x", v0x55a5f7e81b30_0 {0 0 0};
    %alloc S_0x55a5f7e08160;
    %load/vec4 v0x55a5f7e81970_0;
    %load/vec4 v0x55a5f7e81b30_0;
    %store/vec4 v0x55a5f7e7a990_0, 0, 32;
    %store/vec4 v0x55a5f7d89fa0_0, 0, 134;
    %callf/vec4 TD_tile_decoder_pkg.add_word_to_collector, S_0x55a5f7e08160;
    %free S_0x55a5f7e08160;
    %store/vec4 v0x55a5f7e81970_0, 0, 134;
    %load/vec4 v0x55a5f7e81970_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0 T_10.10, 8;
    %pushi/vec4 5850451, 0, 24; draw_string_vec4
    %jmp/1 T_10.11, 8;
T_10.10 ; End of true expr.
    %pushi/vec4 20047, 0, 24; draw_string_vec4
    %jmp/0 T_10.11, 8;
 ; End of false expr.
    %blend;
T_10.11;
    %vpi_call/w 3 584 "$display", "  Complete: %s", S<0,vec4,u24> {1 0 0};
    %load/vec4 v0x55a5f7e81970_0;
    %parti/u 1, 1, 32;
    %flag_set/vec4 8;
    %jmp/0xz  T_10.12, 8;
    %alloc S_0x55a5f7e7aa70;
    %load/vec4 v0x55a5f7e81970_0;
    %parti/u 128, 6, 32;
    %load/vec4 v0x55a5f7e81970_0;
    %parti/u 2, 2, 32;
    %store/vec4 v0x55a5f7e7aeb0_0, 0, 2;
    %store/vec4 v0x55a5f7e7adf0_0, 0, 128;
    %callf/str TD_tile_decoder_pkg.disassemble_instruction, S_0x55a5f7e7aa70;
    %free S_0x55a5f7e7aa70;
    %store/str v0x55a5f7e81a70_0;
    %vpi_call/w 3 589 "$display", "  Disassembly: %s", v0x55a5f7e81a70_0 {0 0 0};
T_10.12 ;
T_10.8 ;
    %vpi_call/w 3 593 "$display", "\012=== Example Complete ===" {0 0 0};
    %end;
    .scope S_0x55a5f7e07fd0;
t_32 %join;
    %end;
    .thread T_10;
# The file index is used to find the file name in the following table.
:file_names 5;
    "N/A";
    "<interactive>";
    "-";
    "tile_instruction_decoder.sv";
    "final_test.sv";
