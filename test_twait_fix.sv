// Test program to verify the twait instruction fix
// This tests the specific case of instruction 0x7080507B

import tile_decoder_pkg::*;

module test_twait_fix;

    initial begin
        logic [31:0] test_instruction;
        instr_collector_t collector;
        string disasm_result;
        logic [6:0] ace_op;
        logic [2:0] tuop;
        logic [2:0] ctrluop;
        logic [2:0] waitop;
        logic isMem;

        $display("=== Testing twait instruction fix ===");
        
        // Test the specific instruction that was failing: 0x7080507B
        test_instruction = 32'h7080507B;
        $display("\nTesting instruction: 0x%08x", test_instruction);
        
        // Analyze the instruction fields
        ace_op = test_instruction[6:0];
        tuop = test_instruction[14:12];
        ctrluop = test_instruction[25:23];
        waitop = test_instruction[30:28];
        isMem = test_instruction[26];
        
        $display("  ACE_OP: %b (%d)", ace_op, ace_op);
        $display("  tuop: %b (%d)", tuop, tuop);
        $display("  ctrluop: %b (%d)", ctrluop, ctrluop);
        $display("  waitop: %b (%d)", waitop, waitop);
        $display("  isMem: %b (%d)", isMem, isMem);
        
        // Check if it's recognized as a tile instruction
        if (tile_decoder_pkg::is_tile_instruction(test_instruction)) begin
            $display("  Is tile instruction: YES");
            
            // Initialize collector
            collector = tile_decoder_pkg::init_collector(test_instruction);
            $display("  Expected length: %0d", collector.expected_length);
            $display("  Is complete: %s", collector.is_complete ? "YES" : "NO");
            
            if (collector.is_complete) begin
                // Disassemble the instruction
                disasm_result = tile_decoder_pkg::disassemble_instruction(
                    collector.instruction_data, collector.expected_length);
                $display("  Disassembly: %s", disasm_result);
                
                // Check if it correctly identifies as twait
                if (disasm_result.substr(0, 4) == "twait") begin
                    $display("  ✓ PASS: Correctly identified as twait instruction");
                end else begin
                    $display("  ✗ FAIL: Expected twait, got: %s", disasm_result);
                end
            end else begin
                $display("  ✗ FAIL: Instruction should be complete for 32-bit");
            end
        end else begin
            $display("  ✗ FAIL: Not recognized as tile instruction");
        end
        
        // Test a few more twait variants
        $display("\n=== Testing other twait variants ===");
        
        // Test twait.i.load.global (waitop=000, isShare=0, isStore=0)
        test_instruction = 32'h0000507B; // Basic pattern with waitop=000
        $display("\nTesting twait.i.load.global: 0x%08x", test_instruction);
        collector = tile_decoder_pkg::init_collector(test_instruction);
        if (collector.is_complete) begin
            disasm_result = tile_decoder_pkg::disassemble_instruction(
                collector.instruction_data, collector.expected_length);
            $display("  Disassembly: %s", disasm_result);
        end
        
        // Test twait.i.store.share (waitop=000, isShare=1, isStore=1)
        test_instruction = 32'h0C00507B; // waitop=000, isShare=1, isStore=1
        $display("\nTesting twait.i.store.share: 0x%08x", test_instruction);
        collector = tile_decoder_pkg::init_collector(test_instruction);
        if (collector.is_complete) begin
            disasm_result = tile_decoder_pkg::disassemble_instruction(
                collector.instruction_data, collector.expected_length);
            $display("  Disassembly: %s", disasm_result);
        end
        
        // Test tsync.i (ctrluop=000)
        test_instruction = 32'h0000507B; // ctrluop=000, tuop=101
        $display("\nTesting tsync.i: 0x%08x", test_instruction);
        collector = tile_decoder_pkg::init_collector(test_instruction);
        if (collector.is_complete) begin
            disasm_result = tile_decoder_pkg::disassemble_instruction(
                collector.instruction_data, collector.expected_length);
            $display("  Disassembly: %s", disasm_result);
        end
        
        $display("\n=== Test Complete ===");
    end

endmodule
