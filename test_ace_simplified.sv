// Test simplified ACE instruction support
import tile_decoder_pkg::*;

module test_ace_simplified;

    initial begin
        logic [31:0] test_instruction;
        instr_collector_t collector;
        string disasm_result;

        $display("=== Testing simplified ACE instruction support ===");
        
        // Test ace_bsync instruction
        // Based on wavedrom: tuop=111, miscop=000, ace_misc_en=1, sync_id in [19:15]
        // Let's create a test instruction: sync_id=5
        test_instruction = 32'h0000_0000;
        test_instruction[6:0] = 7'b1111011;    // ACE_OP
        test_instruction[14:12] = 3'b111;      // tuop = 111
        test_instruction[19:15] = 5'd5;        // sync_id = 5
        test_instruction[28:26] = 3'b000;      // miscop = 000 (ace_bsync)
        test_instruction[31] = 1'b1;           // ace_misc_en = 1
        
        $display("\nTesting ace_bsync instruction: 0x%08x", test_instruction);
        $display("  sync_id = %d", test_instruction[19:15]);
        $display("  miscop = %b (should be 000 for ace_bsync)", test_instruction[28:26]);
        $display("  ace_misc_en = %b (should be 1)", test_instruction[31]);
        
        if (tile_decoder_pkg::is_tile_instruction(test_instruction)) begin
            $display("✓ Recognized as tile instruction");
            
            collector = tile_decoder_pkg::init_collector(test_instruction);
            $display("✓ Expected length: %0d (32-bit)", collector.expected_length);
            $display("✓ Is complete: %s", collector.is_complete ? "YES" : "NO");
            
            if (collector.is_complete) begin
                disasm_result = tile_decoder_pkg::disassemble_instruction(
                    collector.instruction_data, collector.expected_length);
                $display("✓ Disassembly: %s", disasm_result);
                
                if (disasm_result == "ace_bsync x5") begin
                    $display("✓ SUCCESS: ace_bsync correctly identified with sync_id");
                end else begin
                    $display("✗ FAILURE: Expected 'ace_bsync x5', got '%s'", disasm_result);
                end
            end
        end else begin
            $display("✗ FAILURE: Not recognized as tile instruction");
        end
        
        // Test ace_nbsync instruction
        test_instruction = 32'h0000_0000;
        test_instruction[6:0] = 7'b1111011;    // ACE_OP
        test_instruction[14:12] = 3'b111;      // tuop = 111
        test_instruction[19:15] = 5'd10;       // sync_id = 10
        test_instruction[28:26] = 3'b010;      // miscop = 010 (ace_nbsync)
        test_instruction[31] = 1'b1;           // ace_misc_en = 1
        
        $display("\nTesting ace_nbsync instruction: 0x%08x", test_instruction);
        $display("  sync_id = %d", test_instruction[19:15]);
        $display("  miscop = %b (should be 010 for ace_nbsync)", test_instruction[28:26]);
        $display("  ace_misc_en = %b (should be 1)", test_instruction[31]);
        
        if (tile_decoder_pkg::is_tile_instruction(test_instruction)) begin
            $display("✓ Recognized as tile instruction");
            
            collector = tile_decoder_pkg::init_collector(test_instruction);
            $display("✓ Expected length: %0d (32-bit)", collector.expected_length);
            $display("✓ Is complete: %s", collector.is_complete ? "YES" : "NO");
            
            if (collector.is_complete) begin
                disasm_result = tile_decoder_pkg::disassemble_instruction(
                    collector.instruction_data, collector.expected_length);
                $display("✓ Disassembly: %s", disasm_result);
                
                if (disasm_result == "ace_nbsync x10") begin
                    $display("✓ SUCCESS: ace_nbsync correctly identified with sync_id");
                end else begin
                    $display("✗ FAILURE: Expected 'ace_nbsync x10', got '%s'", disasm_result);
                end
            end
        end else begin
            $display("✗ FAILURE: Not recognized as tile instruction");
        end
        
        // Test unknown ACE instruction (miscop not 000 or 010)
        test_instruction = 32'h0000_0000;
        test_instruction[6:0] = 7'b1111011;    // ACE_OP
        test_instruction[14:12] = 3'b111;      // tuop = 111
        test_instruction[19:15] = 5'd1;        // sync_id = 1
        test_instruction[28:26] = 3'b001;      // miscop = 001 (unknown)
        test_instruction[31] = 1'b1;           // ace_misc_en = 1
        
        $display("\nTesting unknown ACE instruction: 0x%08x", test_instruction);
        
        if (tile_decoder_pkg::is_tile_instruction(test_instruction)) begin
            collector = tile_decoder_pkg::init_collector(test_instruction);
            if (collector.is_complete) begin
                disasm_result = tile_decoder_pkg::disassemble_instruction(
                    collector.instruction_data, collector.expected_length);
                $display("✓ Disassembly: %s", disasm_result);
                
                if (disasm_result == "unknown_ace_misc 0") begin
                    $display("✓ SUCCESS: Unknown ACE instruction correctly handled");
                end else begin
                    $display("  Note: Got '%s' (acceptable for unknown instruction)", disasm_result);
                end
            end
        end
        
        $display("\n=== Regression test - ensure other instructions still work ===");
        
        // Test twait instruction
        test_instruction = 32'h7080507B;
        $display("Testing twait instruction: 0x%08x", test_instruction);
        collector = tile_decoder_pkg::init_collector(test_instruction);
        if (collector.is_complete) begin
            disasm_result = tile_decoder_pkg::disassemble_instruction(
                collector.instruction_data, collector.expected_length);
            $display("  Result: %s", disasm_result);
            if (disasm_result == "twait") begin
                $display("✓ twait instruction still works");
            end
        end
        
        // Test CSR instruction
        test_instruction = 32'h0000407B;
        $display("Testing CSR instruction: 0x%08x", test_instruction);
        collector = tile_decoder_pkg::init_collector(test_instruction);
        if (collector.is_complete) begin
            disasm_result = tile_decoder_pkg::disassemble_instruction(
                collector.instruction_data, collector.expected_length);
            $display("  Result: %s", disasm_result);
            if (disasm_result == "tcsrw.i 0") begin
                $display("✓ CSR instruction still works");
            end
        end
        
        $display("\n=== Summary ===");
        $display("✓ Simplified ACE instruction support");
        $display("✓ Only ace_bsync and ace_nbsync are supported");
        $display("✓ Removed complex memory operation parsing");
        $display("✓ Maintained compatibility with existing instructions");
        
        $display("\n=== Test Complete ===");
    end

endmodule
