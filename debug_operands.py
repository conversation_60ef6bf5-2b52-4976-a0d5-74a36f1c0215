#!/usr/bin/env python3
"""
Debug the operand extraction for instruction 0x8003907b8200647b
"""

def debug_operands():
    instruction = 0x8003907b8200647b
    
    print(f"调试指令: 0x{instruction:016x}")
    print()
    
    # 分解为32位字
    word1 = instruction & 0xFFFFFFFF  # 低32位
    word2 = (instruction >> 32) & 0xFFFFFFFF  # 高32位
    
    print(f"Word 1 (低32位): 0x{word1:08x}")
    print(f"Word 2 (高32位): 0x{word2:08x}")
    print()
    
    # 以二进制显示便于分析位字段
    print("二进制表示:")
    print(f"Word 1: {word1:032b}")
    print(f"Word 2: {word2:032b}")
    print()
    
    # 根据SystemVerilog代码分析字段提取
    print("=== SystemVerilog字段提取分析 ===")
    print()
    
    # 对于 tld.trr.blk 指令，SystemVerilog代码中的字段提取：
    # td = instruction_data[32+30:32+23];  // Td field in second word bits [30:23]
    # rs1 = instruction_data[32+19:32+15]; // rs1 field in second word bits [19:15]  
    # rs2 = instruction_data[24:20];       // rs3 field in first word bits [24:20]
    
    # 提取字段
    td_sv = (word2 >> 23) & 0xFF  # bits [30:23] of second word
    rs1_sv = (word2 >> 15) & 0x1F  # bits [19:15] of second word
    rs2_sv = (word1 >> 20) & 0x1F  # bits [24:20] of first word
    
    print("SystemVerilog字段提取:")
    print(f"  td (word2[30:23]):  {td_sv} (0x{td_sv:02x})")
    print(f"  rs1 (word2[19:15]): {rs1_sv}")
    print(f"  rs2 (word1[24:20]): {rs2_sv}")
    print()
    
    # 详细分析每个字段的位提取
    print("详细位分析:")
    print(f"Word 2: 0x{word2:08x} = {word2:032b}")
    print("位位置:  31 30 29 28 27 26 25 24 23 22 21 20 19 18 17 16 15 14 13 12 11 10  9  8  7  6  5  4  3  2  1  0")
    
    # 手动提取位
    word2_bits = [(word2 >> i) & 1 for i in range(32)]
    word2_bits.reverse()  # 反转以匹配位序号
    
    print("Word 2位:  ", end="")
    for i, bit in enumerate(word2_bits):
        print(f"{bit:2}", end="")
    print()
    
    # 标记字段位置
    print("字段标记: ", end="")
    for i in range(32):
        if 23 <= i <= 30:  # td字段
            print(" T", end="")
        elif 15 <= i <= 19:  # rs1字段
            print(" R", end="")
        else:
            print("  ", end="")
    print()
    print()
    
    # 手动计算字段值
    td_manual = 0
    for i in range(23, 31):  # bits 23-30
        if i < 32:
            td_manual |= ((word2 >> i) & 1) << (i - 23)
    
    rs1_manual = 0
    for i in range(15, 20):  # bits 15-19
        rs1_manual |= ((word2 >> i) & 1) << (i - 15)
    
    print("手动计算验证:")
    print(f"  td (手动):  {td_manual} (0x{td_manual:02x})")
    print(f"  rs1 (手动): {rs1_manual}")
    print()
    
    # 分析Word 1的rs2字段
    print(f"Word 1: 0x{word1:08x} = {word1:032b}")
    print("位位置:  31 30 29 28 27 26 25 24 23 22 21 20 19 18 17 16 15 14 13 12 11 10  9  8  7  6  5  4  3  2  1  0")
    
    word1_bits = [(word1 >> i) & 1 for i in range(32)]
    word1_bits.reverse()
    
    print("Word 1位:  ", end="")
    for i, bit in enumerate(word1_bits):
        print(f"{bit:2}", end="")
    print()
    
    print("字段标记: ", end="")
    for i in range(32):
        if 20 <= i <= 24:  # rs2字段
            print(" R", end="")
        else:
            print("  ", end="")
    print()
    print()
    
    rs2_manual = 0
    for i in range(20, 25):  # bits 20-24
        rs2_manual |= ((word1 >> i) & 1) << (i - 20)
    
    print("rs2手动计算:")
    print(f"  rs2 (手动): {rs2_manual}")
    print()
    
    # 比较结果
    print("=== 结果比较 ===")
    print(f"SystemVerilog提取: td={td_sv}, rs1={rs1_sv}, rs2={rs2_sv}")
    print(f"手动计算验证:     td={td_manual}, rs1={rs1_manual}, rs2={rs2_manual}")
    print()
    
    # 格式化操作数
    operands_sv = f"t{td_sv}, (x{rs1_sv}), x{rs2_sv}"
    print(f"SystemVerilog操作数: {operands_sv}")
    print(f"期望的操作数:       t0, (x7), x0")
    print()
    
    # 检查是否匹配期望
    if td_sv == 0 and rs1_sv == 7 and rs2_sv == 0:
        print("✓ 字段提取正确!")
    else:
        print("✗ 字段提取不正确!")
        print("需要检查SystemVerilog代码中的位字段定义")

if __name__ == "__main__":
    debug_operands()
