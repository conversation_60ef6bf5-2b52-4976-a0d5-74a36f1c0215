#!/usr/bin/env python3
"""
Test script to simulate SystemVerilog logic for instruction 0x8003907b8200647b
"""

def test_sv_logic():
    # Test instruction
    instruction = 0x8003907b8200647b
    
    # Split into words
    word1 = instruction & 0xFFFFFFFF  # Low 32 bits
    word2 = (instruction >> 32) & 0xFFFFFFFF  # High 32 bits
    
    print(f"Testing instruction: 0x{instruction:016x}")
    print(f"Word 1 (low):  0x{word1:08x}")
    print(f"Word 2 (high): 0x{word2:08x}")
    print()
    
    # Extract fields from word1
    ace_op1 = word1 & 0x7F
    lsuop1 = (word1 >> 10) & 0x3
    tuop1 = (word1 >> 12) & 0x7
    rs3 = (word1 >> 20) & 0x1F
    memuop1 = (word1 >> 25) & 0x3F
    
    print("Word 1 fields:")
    print(f"  ACE_OP: 0x{ace_op1:02x}")
    print(f"  lsuop:  {lsuop1}")
    print(f"  tuop:   {tuop1}")
    print(f"  rs3:    {rs3}")
    print(f"  memuop: {memuop1}")
    print()
    
    # Extract fields from word2
    ace_op2 = word2 & 0x7F
    tuop2 = (word2 >> 12) & 0x7
    rs1 = (word2 >> 15) & 0x1F
    tilesize = (word2 >> 20) & 0x7
    td = (word2 >> 23) & 0xFF
    
    print("Word 2 fields:")
    print(f"  ACE_OP: 0x{ace_op2:02x}")
    print(f"  tuop:   {tuop2}")
    print(f"  rs1:    {rs1}")
    print(f"  tilesize: {tilesize}")
    print(f"  Td:     {td}")
    print()
    
    # Simulate SystemVerilog logic
    print("SystemVerilog logic simulation:")
    
    # 1. is_tile_instruction(word1)
    is_tile = (ace_op1 == 0x7b)
    print(f"1. is_tile_instruction: {is_tile}")
    
    # 2. get_instruction_length(word1)
    if ace_op1 != 0x7b:
        length = "INSTR_32BIT"
    else:
        if tuop1 == 6:  # tuop_110
            if memuop1 == 1:  # 000001
                length = "INSTR_64BIT"
            else:
                length = "INSTR_32BIT"
        else:
            length = "INSTR_64BIT"  # Default for other tuop values
    
    print(f"2. get_instruction_length: {length}")
    
    # 3. Simulate collector
    if is_tile and length == "INSTR_64BIT":
        print("3. Collector would be initialized, not complete after word1")
        print("4. After adding word2, collector would be complete")
        
        # 5. disassemble_instruction
        print("5. disassemble_instruction:")
        
        # Check tuop values
        if tuop1 == 6 and tuop2 == 1 and memuop1 == 1 and lsuop1 == 1:
            instr_name = "tld.trr.blk.mx48.share"
            operands = f"t{td}, (x{rs1}), x{rs3}"
            result = f"{instr_name} {operands}"
            print(f"   Result: {result}")
        else:
            print(f"   No match found")
            print(f"   tuop1={tuop1}, tuop2={tuop2}, memuop1={memuop1}, lsuop1={lsuop1}")
    
    print()
    print("Expected result: tld.trr.blk.mx48.share t0, (x7), x0")
    
    # Compare with Python disassembler
    print("\nComparing with Python disassembler:")
    from tile_disassembler import TileDisassembler
    disasm = TileDisassembler()
    python_result = disasm.disassemble_instruction(instruction, 64)
    print(f"Python result: {python_result}")

if __name__ == "__main__":
    test_sv_logic()
