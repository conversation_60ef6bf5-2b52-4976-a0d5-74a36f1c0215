#!/usr/bin/env python3
"""
Verify what values would be extracted using various wrong bit positions
"""

def verify_positions():
    instruction = 0x8003907b8200647b
    word1 = instruction & 0xFFFFFFFF
    word2 = (instruction >> 32) & 0xFFFFFFFF
    
    print("=== 验证各种位位置的提取结果 ===")
    print(f"指令: 0x{instruction:016x}")
    print(f"Word 1: 0x{word1:08x}")
    print(f"Word 2: 0x{word2:08x}")
    print()
    
    # 目标：找到能产生 td=123, rs1=3 的位位置
    target_td = 123
    target_rs1 = 3
    
    print(f"目标值: td={target_td}, rs1={target_rs1}")
    print()
    
    # 测试各种可能的错误位位置
    test_cases = [
        # 描述, td位位置, rs1位位置
        ("当前正确位置", (62, 55), (51, 47)),
        ("旧的错误位置", (63, 56), (51, 47)),  # 32+31:32+24, 32+19:32+15
        ("word2[30:23], word2[19:15]", (30+32, 23+32), (19+32, 15+32)),
        ("word2[31:24], word2[20:16]", (31+32, 24+32), (20+32, 16+32)),
        ("直接从word2[30:23]", (30, 23), (19, 15)),  # 相对于word2的位置
        ("bits [39:32]", (39, 32), (51, 47)),  # 从分析中得出的可能位置
        ("bits [39:32], [41:37]", (39, 32), (41, 37)),
        ("bits [39:32], [52:48]", (39, 32), (52, 48)),
    ]
    
    print("测试各种位位置:")
    for desc, (td_high, td_low), (rs1_high, rs1_low) in test_cases:
        # 提取td
        if td_high >= 32:
            # 从完整指令提取
            td_bits = td_high - td_low + 1
            td_val = (instruction >> td_low) & ((1 << td_bits) - 1)
        else:
            # 从word2提取
            td_bits = td_high - td_low + 1
            td_val = (word2 >> td_low) & ((1 << td_bits) - 1)
        
        # 提取rs1
        if rs1_high >= 32:
            # 从完整指令提取
            rs1_bits = rs1_high - rs1_low + 1
            rs1_val = (instruction >> rs1_low) & ((1 << rs1_bits) - 1)
        else:
            # 从word2提取
            rs1_bits = rs1_high - rs1_low + 1
            rs1_val = (word2 >> rs1_low) & ((1 << rs1_bits) - 1)
        
        match_td = "✓" if td_val == target_td else " "
        match_rs1 = "✓" if rs1_val == target_rs1 else " "
        
        print(f"  {desc:25}: td={td_val:3} {match_td}, rs1={rs1_val:2} {match_rs1}")
    
    print()
    
    # 特别检查能产生123的位位置
    print("=== 详细分析产生td=123的位位置 ===")
    
    # 在64位指令中搜索所有能产生123的8位字段
    for start_bit in range(64-8+1):
        value = (instruction >> start_bit) & 0xFF
        if value == target_td:
            end_bit = start_bit + 7
            print(f"bits [{end_bit}:{start_bit}] = {value}")
            
            # 检查这个位置在SystemVerilog中如何表示
            if start_bit >= 32:
                sv_notation = f"instruction_data[{end_bit}:{start_bit}]"
            else:
                sv_notation = f"word2[{end_bit}:{start_bit}] (相对于word2)"
            print(f"  SystemVerilog: {sv_notation}")
    
    print()
    
    # 检查能产生rs1=3的位位置
    print("=== 详细分析产生rs1=3的位位置 ===")
    
    for start_bit in range(64-5+1):
        value = (instruction >> start_bit) & 0x1F
        if value == target_rs1:
            end_bit = start_bit + 4
            print(f"bits [{end_bit}:{start_bit}] = {value}")
            
            if start_bit >= 32:
                sv_notation = f"instruction_data[{end_bit}:{start_bit}]"
            else:
                sv_notation = f"word2[{end_bit}:{start_bit}] (相对于word2)"
            print(f"  SystemVerilog: {sv_notation}")
    
    print()
    print("=== 结论 ===")
    print("如果实际输出是 't123, (x3)'，那么最可能的原因是:")
    print("1. 某处代码仍在使用 instruction_data[39:32] 提取td")
    print("2. 某处代码仍在使用 instruction_data[41:37] 或类似位置提取rs1")
    print("3. 需要检查所有字段提取的地方，确保都使用了正确的位位置")

if __name__ == "__main__":
    verify_positions()
