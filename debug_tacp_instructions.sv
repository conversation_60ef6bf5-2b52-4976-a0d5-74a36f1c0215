// Debug specific tacp instructions
// Test the two instructions: 0x380ee0fb and 0x8e05017b

`include "tile_instruction_decoder.sv"

import tile_decoder_pkg::*;

module debug_tacp_instructions;

    logic [31:0] word1, word2, word3, word4;
    instr_collector_t collector;
    string result;
    logic [6:0] ace_op, ace_op2;
    logic [2:0] tuop, tuop2;
    logic [5:0] memuop, memuop2;
    logic [1:0] lsuop, lsuop2;
    logic is_tile;
    instr_length_e expected_length;

    initial begin
        $display("=== Debug TACP Instructions ===");

        // Test the specific instruction sequence
        word1 = 32'h380ee0fb;
        word2 = 32'h8e05017b;

        $display("\nAnalyzing instruction sequence:");
        $display("Word 1: 0x%08x", word1);
        $display("Word 2: 0x%08x", word2);

        // Analyze first word
        $display("\n--- First Word Analysis ---");
        $display("Binary: %032b", word1);

        ace_op = word1[6:0];
        tuop = word1[14:12];
        memuop = word1[30:25];
        lsuop = word1[11:10];
        
        $display("ace_op: 0x%02x (%07b)", ace_op, ace_op);
        $display("tuop: %d (%03b)", tuop, tuop);
        $display("memuop: %d (%06b)", memuop, memuop);
        $display("lsuop: %d (%02b)", lsuop, lsuop);
        
        // Check if it's a tile instruction
        is_tile = tile_decoder_pkg::is_tile_instruction(word1);
        $display("Is tile instruction: %s", is_tile ? "YES" : "NO");

        // Get expected length
        expected_length = tile_decoder_pkg::get_instruction_length(word1);
        case (expected_length)
            INSTR_32BIT:  $display("Expected length: INSTR_32BIT");
            INSTR_64BIT:  $display("Expected length: INSTR_64BIT");
            INSTR_96BIT:  $display("Expected length: INSTR_96BIT");
            INSTR_128BIT: $display("Expected length: INSTR_128BIT");
        endcase
        
        // Initialize collector
        collector = tile_decoder_pkg::init_collector(word1);
        $display("Collector after init:");
        $display("  collected_words: %d", collector.collected_words);
        $write("  expected_length: ");
        case (collector.expected_length)
            INSTR_32BIT:  $display("INSTR_32BIT");
            INSTR_64BIT:  $display("INSTR_64BIT");
            INSTR_96BIT:  $display("INSTR_96BIT");
            INSTR_128BIT: $display("INSTR_128BIT");
        endcase
        $display("  is_complete: %s", collector.is_complete ? "YES" : "NO");
        $display("  is_tile_instr: %s", collector.is_tile_instr ? "YES" : "NO");
        
        // Add second word
        $display("\n--- Adding Second Word ---");
        collector = tile_decoder_pkg::add_word_to_collector(collector, word2);
        $display("Collector after adding word2:");
        $display("  collected_words: %d", collector.collected_words);
        $display("  is_complete: %s", collector.is_complete ? "YES" : "NO");
        $display("  instruction_data: 0x%032x", collector.instruction_data);
        
        // Analyze second word
        $display("\n--- Second Word Analysis ---");
        $display("Binary: %032b", word2);

        ace_op2 = word2[6:0];
        tuop2 = word2[14:12];
        memuop2 = word2[30:25];
        lsuop2 = word2[11:10];
        
        $display("ace_op2: 0x%02x (%07b)", ace_op2, ace_op2);
        $display("tuop2: %d (%03b)", tuop2, tuop2);
        $display("memuop2: %d (%06b)", memuop2, memuop2);
        $display("lsuop2: %d (%02b)", lsuop2, lsuop2);
        
        // Check why it might not be complete
        $display("\n--- Completeness Analysis ---");
        $write("Expected length: ");
        case (collector.expected_length)
            INSTR_32BIT:  $display("INSTR_32BIT");
            INSTR_64BIT:  $display("INSTR_64BIT");
            INSTR_96BIT:  $display("INSTR_96BIT");
            INSTR_128BIT: $display("INSTR_128BIT");
        endcase
        $display("Collected words: %d", collector.collected_words);
        
        case (collector.expected_length)
            INSTR_64BIT:  $display("For 64-bit: need >= 2 words, have %d -> complete: %s", 
                                  collector.collected_words, 
                                  (collector.collected_words >= 2) ? "YES" : "NO");
            INSTR_96BIT:  $display("For 96-bit: need >= 3 words, have %d -> complete: %s", 
                                  collector.collected_words, 
                                  (collector.collected_words >= 3) ? "YES" : "NO");
            INSTR_128BIT: $display("For 128-bit: need >= 4 words, have %d -> complete: %s", 
                                  collector.collected_words, 
                                  (collector.collected_words >= 4) ? "YES" : "NO");
            default:      $display("Default case: should be complete");
        endcase
        
        // Manual check of length determination logic
        $display("\n--- Manual Length Check ---");
        $display("ace_op == TILE_ACE_OP (0x7b): %s", (ace_op == 7'b1111011) ? "YES" : "NO");
        $display("tuop value: %d", tuop);
        
        if (tuop == 3'b000) begin
            $display("tuop_000 path:");
            $display("  lsuop: %d", lsuop);
            if (lsuop == 2'b11) begin
                $display("  lsuop == 11, checking memuop");
                $display("  memuop: %d (0x%02x)", memuop, memuop);
                $display("  memuop == 0x1c (28): %s", (memuop == 6'b011100) ? "YES" : "NO");
                if (memuop == 6'b011100) begin
                    $display("  -> Should return INSTR_128BIT");
                end else begin
                    $display("  -> Should return INSTR_64BIT");
                end
            end
        end else if (tuop == 3'b110) begin
            $display("tuop_110 path:");
            $display("  memuop: %d (0x%02x)", memuop, memuop);
            if (memuop == 6'b011100) begin
                $display("  memuop == 0x1c -> Should return INSTR_128BIT");
            end else begin
                $display("  Other memuop -> Should return INSTR_64BIT");
            end
        end
        
        // Try disassembly if complete
        if (collector.is_complete) begin
            result = tile_decoder_pkg::disassemble_instruction(
                collector.instruction_data, collector.expected_length);
            $display("\nDisassembly result: %s", result);
        end else begin
            $display("\nInstruction not complete, cannot disassemble");
            $display("Need to add 2 more words to complete 128-bit instruction");

            // Let's try adding dummy words to see what happens
            $display("\n--- Testing with dummy words ---");
            word3 = 32'h00000000;  // Dummy word 3
            word4 = 32'h00000000;  // Dummy word 4

            collector = tile_decoder_pkg::add_word_to_collector(collector, word3);
            $display("After adding word3: collected_words=%d, is_complete=%s",
                    collector.collected_words, collector.is_complete ? "YES" : "NO");

            collector = tile_decoder_pkg::add_word_to_collector(collector, word4);
            $display("After adding word4: collected_words=%d, is_complete=%s",
                    collector.collected_words, collector.is_complete ? "YES" : "NO");

            if (collector.is_complete) begin
                result = tile_decoder_pkg::disassemble_instruction(
                    collector.instruction_data, collector.expected_length);
                $display("Disassembly with dummy words: %s", result);
            end
        end

        $display("\n=== Debug Complete ===");
    end

endmodule
