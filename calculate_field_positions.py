#!/usr/bin/env python3
"""
Calculate the correct field positions for tld.trr.blk.mx48.share instruction
based on the wavedrom definition
"""

def calculate_positions():
    # Wavedrom definition for tld.trr.blk.mx48.share
    # From LSB to MSB (bit 0 to bit 63)
    fields = [
        # First 32-bit word (bits 0-31)
        {"bits": 7,  "name": 'ACE_op',    "attr": '1111011'},
        {"bits": 2,  "name": '',    "attr": '00'},
        {"bits": 1,  "name": '',        "attr": '0'},
        {"bits": 2,  "name": 'lsuop',    "attr": '01'},
        {"bits": 3,  "name": 'tuop',     "attr": '110'},
        {"bits": 2,  "name": '',       "attr": '00'},
        {"bits": 2,  "name": '',       "attr": '00'},
        {"bits": 1,  "name": '',       "attr": '0'},
        {"bits": 5,  "name": 'rs3',       "attr": ''},
        {"bits": 6,  "name": 'memuop', "attr":'000001'},
        {"bits": 1,  "name": '',    "attr": '1'},
        
        # Second 32-bit word (bits 32-63)
        {"bits": 7,  "name": 'ACE_op',    "attr": '1111011'},
        {"bits": 5,  "name": '',        "attr": '00000'},
        {"bits": 3,  "name": 'tuop',     "attr": '001'},
        {"bits": 5,  "name": 'rs1',       "attr": ''},
        {"bits": 3,  "name": 'tilesize',       "attr": ''},
        {"bits": 8,  "name": 'Td',       "attr": ''},
        {"bits": 1,  "name": '',     "attr": '1'},
    ]
    
    print("=== 字段位置计算 ===")
    print()
    
    # Calculate bit positions
    current_bit = 0
    field_positions = {}
    
    for field in fields:
        name = field["name"]
        bits = field["bits"]
        
        if name:  # Only track named fields
            start_bit = current_bit
            end_bit = current_bit + bits - 1
            field_positions[name] = {
                "start": start_bit,
                "end": end_bit,
                "bits": bits,
                "word": 1 if current_bit < 32 else 2
            }
            
            print(f"{name:12}: bits [{end_bit:2}:{start_bit:2}] ({bits:2} bits) - Word {1 if current_bit < 32 else 2}")
        
        current_bit += bits
    
    print()
    print("=== 测试指令 0x8003907b8200647b ===")
    
    instruction = 0x8003907b8200647b
    word1 = instruction & 0xFFFFFFFF
    word2 = (instruction >> 32) & 0xFFFFFFFF
    
    print(f"Word 1: 0x{word1:08x}")
    print(f"Word 2: 0x{word2:08x}")
    print()
    
    # Extract field values
    print("字段值提取:")
    for name, pos in field_positions.items():
        if pos["word"] == 1:
            # Extract from word1
            mask = (1 << pos["bits"]) - 1
            value = (word1 >> pos["start"]) & mask
        else:
            # Extract from word2
            mask = (1 << pos["bits"]) - 1
            start_in_word2 = pos["start"] - 32
            value = (word2 >> start_in_word2) & mask
        
        print(f"  {name:12}: {value:3} (0x{value:02x})")
    
    print()
    print("=== SystemVerilog字段提取对比 ===")
    
    # Current SystemVerilog extraction
    td_sv = (word2 >> 23) & 0xFF  # instruction_data[32+30:32+23]
    rs1_sv = (word2 >> 15) & 0x1F  # instruction_data[32+19:32+15]
    rs3_sv = (word1 >> 20) & 0x1F  # instruction_data[24:20]
    
    print("当前SystemVerilog提取:")
    print(f"  td:  {td_sv} (从word2[30:23])")
    print(f"  rs1: {rs1_sv} (从word2[19:15])")
    print(f"  rs3: {rs3_sv} (从word1[24:20])")
    print()
    
    # Correct extraction based on wavedrom
    td_correct = field_positions["Td"]
    rs1_correct = field_positions["rs1"]
    rs3_correct = field_positions["rs3"]
    
    print("正确的字段位置 (基于wavedrom):")
    print(f"  Td:  bits [{td_correct['end']}:{td_correct['start']}] - Word {td_correct['word']}")
    print(f"  rs1: bits [{rs1_correct['end']}:{rs1_correct['start']}] - Word {rs1_correct['word']}")
    print(f"  rs3: bits [{rs3_correct['end']}:{rs3_correct['start']}] - Word {rs3_correct['word']}")
    print()
    
    # Extract correct values
    td_val = (word2 >> (td_correct["start"] - 32)) & ((1 << td_correct["bits"]) - 1)
    rs1_val = (word2 >> (rs1_correct["start"] - 32)) & ((1 << rs1_correct["bits"]) - 1)
    rs3_val = (word1 >> rs3_correct["start"]) & ((1 << rs3_correct["bits"]) - 1)
    
    print("正确提取的值:")
    print(f"  Td:  {td_val}")
    print(f"  rs1: {rs1_val}")
    print(f"  rs3: {rs3_val}")
    print()
    
    # Generate SystemVerilog code
    print("=== 正确的SystemVerilog代码 ===")
    print("```systemverilog")
    print("// 对于 tld.trr.blk 指令的字段提取")
    print(f"td = instruction_data[{32 + td_correct['end']}:{32 + td_correct['start']}];   // Td字段")
    print(f"rs1 = instruction_data[{32 + rs1_correct['end']}:{32 + rs1_correct['start']}]; // rs1字段")
    print(f"rs2 = instruction_data[{rs3_correct['end']}:{rs3_correct['start']}];       // rs3字段")
    print("```")

if __name__ == "__main__":
    calculate_positions()
