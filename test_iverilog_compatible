#! /usr/bin/vvp
:ivl_version "11.0 (stable)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision - 12;
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/system.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_sys.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_textio.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2005_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/va_math.vpi";
S_0x560258349500 .scope module, "test_iverilog_compatible" "test_iverilog_compatible" 2 6;
 .timescale -9 -12;
P_0x5602582f8a20 .param/l "FULL_INSTRUCTION" 0 2 11, C4<1000000000000011100100000111101110000010000000000110010001111011>;
P_0x5602582f8a60 .param/l "WORD1" 0 2 9, C4<10000010000000000110010001111011>;
P_0x5602582f8aa0 .param/l "WORD2" 0 2 10, C4<10000000000000111001000001111011>;
v0x5602583248d0_0 .var "ace_op1", 6 0;
v0x560258324970_0 .var "ace_op2", 6 0;
v0x560258369200_0 .var "lsuop", 1 0;
v0x5602583692c0_0 .var "memuop", 5 0;
v0x5602583693a0_0 .var "rs1_new", 4 0;
v0x5602583694d0_0 .var "rs1_old", 4 0;
v0x5602583695b0_0 .var "rs2_field", 4 0;
v0x560258369690_0 .var "td_new", 7 0;
v0x560258369770_0 .var "td_old", 7 0;
v0x560258369850_0 .var "tuop1", 2 0;
v0x560258369930_0 .var "tuop2", 2 0;
    .scope S_0x560258349500;
T_0 ;
    %vpi_call 2 25 "$display", "=== Iverilog Compatible Test for 0x8003907b8200647b ===" {0 0 0};
    %vpi_call 2 26 "$display", "\000" {0 0 0};
    %vpi_call 2 28 "$display", "Test Data:" {0 0 0};
    %vpi_call 2 29 "$display", "  Full instruction: 0x%016x", P_0x5602582f8a20 {0 0 0};
    %vpi_call 2 30 "$display", "  Word 1 (low):     0x%08x", P_0x5602582f8a60 {0 0 0};
    %vpi_call 2 31 "$display", "  Word 2 (high):    0x%08x", P_0x5602582f8aa0 {0 0 0};
    %vpi_call 2 32 "$display", "\000" {0 0 0};
    %vpi_call 2 35 "$display", "=== Field Extraction Test ===" {0 0 0};
    %vpi_call 2 36 "$display", "Testing OLD (incorrect) field positions:" {0 0 0};
    %pushi/vec4 0, 0, 8;
    %store/vec4 v0x560258369770_0, 0, 8;
    %pushi/vec4 7, 0, 5;
    %store/vec4 v0x5602583694d0_0, 0, 5;
    %pushi/vec4 0, 0, 5;
    %store/vec4 v0x5602583695b0_0, 0, 5;
    %vpi_call 2 42 "$display", "  td_old (bits [62:55]):  %0d", v0x560258369770_0 {0 0 0};
    %vpi_call 2 43 "$display", "  rs1_old (bits [51:47]): %0d", v0x5602583694d0_0 {0 0 0};
    %vpi_call 2 44 "$display", "  rs2 (bits [24:20]):     %0d", v0x5602583695b0_0 {0 0 0};
    %vpi_call 2 45 "$display", "  OLD result: tld.trr.blk.mx48.share t%0d, (x%0d), x%0d", v0x560258369770_0, v0x5602583694d0_0, v0x5602583695b0_0 {0 0 0};
    %vpi_call 2 46 "$display", "\000" {0 0 0};
    %vpi_call 2 49 "$display", "Testing NEW (correct) field positions:" {0 0 0};
    %pushi/vec4 0, 0, 8;
    %store/vec4 v0x560258369690_0, 0, 8;
    %pushi/vec4 7, 0, 5;
    %store/vec4 v0x5602583693a0_0, 0, 5;
    %vpi_call 2 61 "$display", "  td_new (word2[30:23]):  %0d", v0x560258369690_0 {0 0 0};
    %vpi_call 2 62 "$display", "  rs1_new (word2[19:15]): %0d", v0x5602583693a0_0 {0 0 0};
    %vpi_call 2 63 "$display", "  rs2 (word1[24:20]):     %0d", v0x5602583695b0_0 {0 0 0};
    %vpi_call 2 64 "$display", "  NEW result: tld.trr.blk.mx48.share t%0d, (x%0d), x%0d", v0x560258369690_0, v0x5602583693a0_0, v0x5602583695b0_0 {0 0 0};
    %vpi_call 2 65 "$display", "\000" {0 0 0};
    %vpi_call 2 68 "$display", "=== Comparison ===" {0 0 0};
    %load/vec4 v0x560258369770_0;
    %load/vec4 v0x560258369690_0;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x5602583694d0_0;
    %load/vec4 v0x5602583693a0_0;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.0, 8;
    %vpi_call 2 70 "$display", "  Note: For this specific instruction, both methods give the same result" {0 0 0};
    %vpi_call 2 71 "$display", "  This is because the field values happen to align correctly" {0 0 0};
    %jmp T_0.1;
T_0.0 ;
    %vpi_call 2 73 "$display", "  Field extraction differences found:" {0 0 0};
    %vpi_call 2 74 "$display", "    td:  old=%0d, new=%0d", v0x560258369770_0, v0x560258369690_0 {0 0 0};
    %vpi_call 2 75 "$display", "    rs1: old=%0d, new=%0d", v0x5602583694d0_0, v0x5602583693a0_0 {0 0 0};
T_0.1 ;
    %vpi_call 2 77 "$display", "\000" {0 0 0};
    %vpi_call 2 80 "$display", "=== Verification ===" {0 0 0};
    %vpi_call 2 81 "$display", "  Expected result: tld.trr.blk.mx48.share t0, (x7), x0" {0 0 0};
    %load/vec4 v0x560258369690_0;
    %pad/u 32;
    %pushi/vec4 0, 0, 32;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x5602583693a0_0;
    %pad/u 32;
    %pushi/vec4 7, 0, 32;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %load/vec4 v0x5602583695b0_0;
    %pad/u 32;
    %pushi/vec4 0, 0, 32;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.2, 8;
    %vpi_call 2 84 "$display", "  \342\234\223 SUCCESS: Field extraction produces expected values!" {0 0 0};
    %vpi_call 2 85 "$display", "  \342\234\223 td=0 (t0), rs1=7 (x7), rs2=0 (x0)" {0 0 0};
    %jmp T_0.3;
T_0.2 ;
    %vpi_call 2 87 "$display", "  \342\234\227 FAILURE: Field extraction does not match expected values" {0 0 0};
    %vpi_call 2 88 "$display", "  Expected: td=0, rs1=7, rs2=0" {0 0 0};
    %vpi_call 2 89 "$display", "  Actual:   td=%0d, rs1=%0d, rs2=%0d", v0x560258369690_0, v0x5602583693a0_0, v0x5602583695b0_0 {0 0 0};
T_0.3 ;
    %vpi_call 2 91 "$display", "\000" {0 0 0};
    %vpi_call 2 94 "$display", "=== Instruction Identification Test ===" {0 0 0};
    %pushi/vec4 123, 0, 7;
    %store/vec4 v0x5602583248d0_0, 0, 7;
    %pushi/vec4 6, 0, 3;
    %store/vec4 v0x560258369850_0, 0, 3;
    %pushi/vec4 1, 0, 6;
    %store/vec4 v0x5602583692c0_0, 0, 6;
    %pushi/vec4 1, 0, 2;
    %store/vec4 v0x560258369200_0, 0, 2;
    %pushi/vec4 123, 0, 7;
    %store/vec4 v0x560258324970_0, 0, 7;
    %pushi/vec4 1, 0, 3;
    %store/vec4 v0x560258369930_0, 0, 3;
    %vpi_call 2 103 "$display", "  Instruction identification fields:" {0 0 0};
    %load/vec4 v0x5602583248d0_0;
    %cmpi/e 123, 0, 7;
    %flag_mov 8, 4;
    %jmp/0 T_0.4, 8;
    %pushi/vec4 0, 0, 32; draw_string_vec4
    %pushi/vec4 1414089797, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.5, 8;
T_0.4 ; End of true expr.
    %pushi/vec4 1313821791, 0, 32; draw_string_vec4
    %pushi/vec4 1414089797, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.5, 8;
 ; End of false expr.
    %blend;
T_0.5;
    %vpi_call 2 104 "$display", "    ace_op1: 0x%02x (%s)", v0x5602583248d0_0, S<0,vec4,u64> {1 0 0};
    %load/vec4 v0x560258369850_0;
    %cmpi/e 6, 0, 3;
    %flag_mov 8, 4;
    %jmp/0 T_0.6, 8;
    %pushi/vec4 12593, 0, 32; draw_string_vec4
    %pushi/vec4 48, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.7, 8;
T_0.6 ; End of true expr.
    %pushi/vec4 1330923589, 0, 32; draw_string_vec4
    %pushi/vec4 82, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.7, 8;
 ; End of false expr.
    %blend;
T_0.7;
    %vpi_call 2 105 "$display", "    tuop1: %0d (%s)", v0x560258369850_0, S<0,vec4,u40> {1 0 0};
    %load/vec4 v0x5602583692c0_0;
    %cmpi/e 1, 0, 6;
    %flag_mov 8, 4;
    %jmp/0 T_0.8, 8;
    %pushi/vec4 808464432, 0, 32; draw_string_vec4
    %pushi/vec4 12337, 0, 16; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.9, 8;
T_0.8 ; End of true expr.
    %pushi/vec4 5198920, 0, 32; draw_string_vec4
    %pushi/vec4 17746, 0, 16; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.9, 8;
 ; End of false expr.
    %blend;
T_0.9;
    %vpi_call 2 106 "$display", "    memuop: %0d (%s)", v0x5602583692c0_0, S<0,vec4,u48> {1 0 0};
    %load/vec4 v0x560258369200_0;
    %cmpi/e 1, 0, 2;
    %flag_mov 8, 4;
    %jmp/0 T_0.10, 8;
    %pushi/vec4 48, 0, 32; draw_string_vec4
    %pushi/vec4 49, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.11, 8;
T_0.10 ; End of true expr.
    %pushi/vec4 1330923589, 0, 32; draw_string_vec4
    %pushi/vec4 82, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.11, 8;
 ; End of false expr.
    %blend;
T_0.11;
    %vpi_call 2 107 "$display", "    lsuop: %0d (%s)", v0x560258369200_0, S<0,vec4,u40> {1 0 0};
    %vpi_call 2 108 "$display", "    ace_op2: 0x%02x", v0x560258324970_0 {0 0 0};
    %load/vec4 v0x560258369930_0;
    %cmpi/e 1, 0, 3;
    %flag_mov 8, 4;
    %jmp/0 T_0.12, 8;
    %pushi/vec4 12336, 0, 32; draw_string_vec4
    %pushi/vec4 49, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.13, 8;
T_0.12 ; End of true expr.
    %pushi/vec4 1330923589, 0, 32; draw_string_vec4
    %pushi/vec4 82, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.13, 8;
 ; End of false expr.
    %blend;
T_0.13;
    %vpi_call 2 109 "$display", "    tuop2: %0d (%s)", v0x560258369930_0, S<0,vec4,u40> {1 0 0};
    %load/vec4 v0x5602583248d0_0;
    %cmpi/e 123, 0, 7;
    %jmp/0xz  T_0.14, 4;
    %load/vec4 v0x560258369850_0;
    %cmpi/e 6, 0, 3;
    %jmp/0xz  T_0.16, 4;
    %load/vec4 v0x5602583692c0_0;
    %cmpi/e 1, 0, 6;
    %jmp/0xz  T_0.18, 4;
    %load/vec4 v0x560258369200_0;
    %cmpi/e 1, 0, 2;
    %jmp/0xz  T_0.20, 4;
    %load/vec4 v0x560258369930_0;
    %cmpi/e 1, 0, 3;
    %jmp/0xz  T_0.22, 4;
    %vpi_call 2 117 "$display", "  \342\234\223 Instruction correctly identified as tld.trr.blk.mx48.share" {0 0 0};
    %jmp T_0.23;
T_0.22 ;
    %vpi_call 2 119 "$display", "  \342\234\227 tuop2 check failed" {0 0 0};
T_0.23 ;
    %jmp T_0.21;
T_0.20 ;
    %vpi_call 2 122 "$display", "  \342\234\227 lsuop check failed" {0 0 0};
T_0.21 ;
    %jmp T_0.19;
T_0.18 ;
    %vpi_call 2 125 "$display", "  \342\234\227 memuop check failed" {0 0 0};
T_0.19 ;
    %jmp T_0.17;
T_0.16 ;
    %vpi_call 2 128 "$display", "  \342\234\227 tuop1 check failed" {0 0 0};
T_0.17 ;
    %jmp T_0.15;
T_0.14 ;
    %vpi_call 2 131 "$display", "  \342\234\227 ace_op1 check failed" {0 0 0};
T_0.15 ;
    %vpi_call 2 133 "$display", "\000" {0 0 0};
    %vpi_call 2 136 "$display", "=== Binary Analysis ===" {0 0 0};
    %vpi_call 2 137 "$display", "  Instruction binary: %064b", P_0x5602582f8a20 {0 0 0};
    %vpi_call 2 138 "$display", "  Bit positions:      6666555555555544444444443333333333222222222211111111110000000000" {0 0 0};
    %vpi_call 2 139 "$display", "                      3210987654321098765432109876543210987654321098765432109876543210" {0 0 0};
    %vpi_call 2 140 "$display", "\000" {0 0 0};
    %vpi_call 2 141 "$display", "  Field positions:" {0 0 0};
    %vpi_call 2 142 "$display", "    Td [62:55]:   bits 55-62" {0 0 0};
    %vpi_call 2 143 "$display", "    rs1 [51:47]:  bits 47-51" {0 0 0};
    %vpi_call 2 144 "$display", "    rs2 [24:20]:  bits 20-24" {0 0 0};
    %vpi_call 2 145 "$display", "\000" {0 0 0};
    %vpi_call 2 147 "$display", "=== Test Complete ===" {0 0 0};
    %vpi_call 2 148 "$display", "\000" {0 0 0};
    %load/vec4 v0x560258369690_0;
    %pad/u 32;
    %pushi/vec4 0, 0, 32;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x5602583693a0_0;
    %pad/u 32;
    %pushi/vec4 7, 0, 32;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %load/vec4 v0x5602583695b0_0;
    %pad/u 32;
    %pushi/vec4 0, 0, 32;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.24, 8;
    %vpi_call 2 152 "$display", "OVERALL RESULT: \342\234\223 SUCCESS - Field extraction is working correctly!" {0 0 0};
    %vpi_call 2 153 "$display", "The instruction 0x8003907b8200647b correctly decodes to:" {0 0 0};
    %vpi_call 2 154 "$display", "tld.trr.blk.mx48.share t0, (x7), x0" {0 0 0};
    %jmp T_0.25;
T_0.24 ;
    %vpi_call 2 156 "$display", "OVERALL RESULT: \342\234\227 FAILURE - Field extraction needs fixing" {0 0 0};
T_0.25 ;
    %vpi_call 2 159 "$finish" {0 0 0};
    %end;
    .thread T_0;
# The file index is used to find the file name in the following table.
:file_names 3;
    "N/A";
    "<interactive>";
    "test_iverilog_compatible.sv";
