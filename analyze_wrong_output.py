#!/usr/bin/env python3
"""
Analyze what could cause the wrong output t123, (x3)
"""

def analyze_wrong_output():
    instruction = 0x8003907b8200647b
    word1 = instruction & 0xFFFFFFFF
    word2 = (instruction >> 32) & 0xFFFFFFFF
    
    print("=== 分析错误输出 t123, (x3) ===")
    print(f"指令: 0x{instruction:016x}")
    print(f"Word 1: 0x{word1:08x}")
    print(f"Word 2: 0x{word2:08x}")
    print()
    
    # 如果输出是 t123, (x3)，那么：
    # td = 123
    # rs1 = 3
    
    target_td = 123
    target_rs1 = 3
    
    print(f"目标值: td={target_td}, rs1={target_rs1}")
    print(f"td=123 的二进制: {target_td:08b}")
    print(f"rs1=3 的二进制: {target_rs1:05b}")
    print()
    
    # 检查在64位指令中哪些位位置会产生这些值
    print("=== 寻找可能的错误位位置 ===")
    
    # 检查word2中所有可能的8位字段位置，看哪个会产生123
    print("Word 2中可能产生td=123的位位置:")
    for start_bit in range(32-8+1):  # 0 to 24
        value = (word2 >> start_bit) & 0xFF
        if value == target_td:
            end_bit = start_bit + 7
            print(f"  bits [{end_bit+32}:{start_bit+32}] = {value}")
    
    # 检查word2中所有可能的5位字段位置，看哪个会产生3
    print("Word 2中可能产生rs1=3的位位置:")
    for start_bit in range(32-5+1):  # 0 to 27
        value = (word2 >> start_bit) & 0x1F
        if value == target_rs1:
            end_bit = start_bit + 4
            print(f"  bits [{end_bit+32}:{start_bit+32}] = {value}")
    
    print()
    
    # 检查是否有字节序问题
    print("=== 检查字节序问题 ===")
    
    # 如果指令的字节序被颠倒了
    instruction_swapped = 0
    for i in range(8):
        byte_val = (instruction >> (i * 8)) & 0xFF
        instruction_swapped |= byte_val << ((7-i) * 8)
    
    print(f"字节序颠倒后: 0x{instruction_swapped:016x}")
    
    word1_swapped = instruction_swapped & 0xFFFFFFFF
    word2_swapped = (instruction_swapped >> 32) & 0xFFFFFFFF
    
    print(f"Word 1 (颠倒): 0x{word1_swapped:08x}")
    print(f"Word 2 (颠倒): 0x{word2_swapped:08x}")
    
    # 检查颠倒后的字段
    td_swapped = (word2_swapped >> 23) & 0xFF
    rs1_swapped = (word2_swapped >> 15) & 0x1F
    
    print(f"颠倒后的字段: td={td_swapped}, rs1={rs1_swapped}")
    
    print()
    
    # 检查是否有位位置计算错误
    print("=== 检查常见的位位置错误 ===")
    
    # 错误1: 使用了错误的基址
    print("可能的错误位位置计算:")
    
    # 如果使用了错误的偏移
    for offset in [0, 8, 16, 24]:
        td_test = (word2 >> (23 + offset)) & 0xFF if (23 + offset) < 32 else 0
        rs1_test = (word2 >> (15 + offset)) & 0x1F if (15 + offset) < 32 else 0
        
        if td_test == target_td or rs1_test == target_rs1:
            print(f"  偏移 {offset}: td={td_test}, rs1={rs1_test}")
    
    # 检查是否使用了word1而不是word2
    print("如果错误地从word1提取:")
    td_from_word1 = (word1 >> 23) & 0xFF
    rs1_from_word1 = (word1 >> 15) & 0x1F
    print(f"  从word1: td={td_from_word1}, rs1={rs1_from_word1}")
    
    print()
    
    # 检查SystemVerilog位选择语法问题
    print("=== SystemVerilog位选择语法检查 ===")
    print("当前代码:")
    print("  td = instruction_data[62:55];")
    print("  rs1 = instruction_data[51:47];")
    print()
    print("可能的问题:")
    print("1. 位选择范围错误")
    print("2. 数据类型问题")
    print("3. 信号宽度不匹配")
    print("4. 时序问题")
    
    # 最可能的原因
    print()
    print("=== 最可能的原因 ===")
    print("基于输出 't123, (x3)'，最可能的原因是:")
    print("1. SystemVerilog代码中仍然使用了旧的字段位置")
    print("2. 修复没有正确应用到所有相关位置")
    print("3. 存在多个字段提取函数，只修复了其中一个")
    print("4. 编译/仿真缓存问题")
    
    # 建议的调试步骤
    print()
    print("=== 建议的调试步骤 ===")
    print("1. 确认SystemVerilog代码确实已经更新")
    print("2. 重新编译所有文件")
    print("3. 添加调试输出显示中间字段值")
    print("4. 检查是否有其他format_operands函数")
    print("5. 验证指令数据在传递过程中没有被修改")

if __name__ == "__main__":
    analyze_wrong_output()
