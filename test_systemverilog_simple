#! /usr/bin/vvp
:ivl_version "11.0 (stable)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision - 12;
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/system.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_sys.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_textio.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2005_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/va_math.vpi";
S_0x564c4c16d0e0 .scope module, "test_systemverilog_simple" "test_systemverilog_simple" 2 6;
 .timescale -9 -12;
P_0x564c4c126a20 .param/l "FULL_INSTRUCTION" 0 2 11, C4<1000000000000011100100000111101110000010000000000110010001111011>;
P_0x564c4c126a60 .param/l "WORD1" 0 2 9, C4<10000010000000000110010001111011>;
P_0x564c4c126aa0 .param/l "WORD2" 0 2 10, C4<10000000000000111001000001111011>;
v0x564c4c14f360_0 .var "ace_op", 6 0;
v0x564c4c18cfa0_0 .var "instruction_data", 127 0;
v0x564c4c18d080_0 .var "lsuop", 1 0;
v0x564c4c18d140_0 .var "memuop", 5 0;
v0x564c4c18d220_0 .var "rs1", 4 0;
v0x564c4c18d350_0 .var "rs2", 4 0;
v0x564c4c18d430_0 .var "second_tuop", 2 0;
v0x564c4c18d510_0 .var "td", 7 0;
v0x564c4c18d5f0_0 .var "tuop", 2 0;
    .scope S_0x564c4c16d0e0;
T_0 ;
    %vpi_call 2 26 "$display", "=== SystemVerilog Field Extraction Test ===" {0 0 0};
    %vpi_call 2 27 "$display", "\000" {0 0 0};
    %vpi_call 2 29 "$display", "Test Data:" {0 0 0};
    %vpi_call 2 30 "$display", "  Full instruction: 0x%016x", P_0x564c4c126a20 {0 0 0};
    %vpi_call 2 31 "$display", "  Word 1 (low):     0x%08x", P_0x564c4c126a60 {0 0 0};
    %vpi_call 2 32 "$display", "  Word 2 (high):    0x%08x", P_0x564c4c126aa0 {0 0 0};
    %vpi_call 2 33 "$display", "\000" {0 0 0};
    %pushi/vec4 2147717243, 0, 96;
    %concati/vec4 2181063803, 0, 32;
    %store/vec4 v0x564c4c18cfa0_0, 0, 128;
    %vpi_call 2 37 "$display", "  instruction_data: 0x%032x", v0x564c4c18cfa0_0 {0 0 0};
    %vpi_call 2 38 "$display", "\000" {0 0 0};
    %vpi_call 2 41 "$display", "=== Testing FIXED Field Extraction ===" {0 0 0};
    %vpi_call 2 42 "$display", "Using corrected SystemVerilog field positions:" {0 0 0};
    %load/vec4 v0x564c4c18cfa0_0;
    %parti/s 8, 55, 7;
    %store/vec4 v0x564c4c18d510_0, 0, 8;
    %load/vec4 v0x564c4c18cfa0_0;
    %parti/s 5, 47, 7;
    %store/vec4 v0x564c4c18d220_0, 0, 5;
    %load/vec4 v0x564c4c18cfa0_0;
    %parti/s 5, 20, 6;
    %store/vec4 v0x564c4c18d350_0, 0, 5;
    %vpi_call 2 53 "$display", "  td = instruction_data[32+30:32+23] = instruction_data[62:55] = %0d", v0x564c4c18d510_0 {0 0 0};
    %vpi_call 2 54 "$display", "  rs1 = instruction_data[32+19:32+15] = instruction_data[51:47] = %0d", v0x564c4c18d220_0 {0 0 0};
    %vpi_call 2 55 "$display", "  rs2 = instruction_data[24:20] = %0d", v0x564c4c18d350_0 {0 0 0};
    %vpi_call 2 56 "$display", "\000" {0 0 0};
    %vpi_call 2 59 "$display", "  Formatted operands: t%0d, (x%0d), x%0d", v0x564c4c18d510_0, v0x564c4c18d220_0, v0x564c4c18d350_0 {0 0 0};
    %vpi_call 2 60 "$display", "  Complete instruction: tld.trr.blk.mx48.share t%0d, (x%0d), x%0d", v0x564c4c18d510_0, v0x564c4c18d220_0, v0x564c4c18d350_0 {0 0 0};
    %vpi_call 2 61 "$display", "\000" {0 0 0};
    %vpi_call 2 64 "$display", "=== Verification ===" {0 0 0};
    %vpi_call 2 65 "$display", "  Expected: tld.trr.blk.mx48.share t0, (x7), x0" {0 0 0};
    %load/vec4 v0x564c4c18d510_0;
    %pad/u 32;
    %pushi/vec4 0, 0, 32;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x564c4c18d220_0;
    %pad/u 32;
    %pushi/vec4 7, 0, 32;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %load/vec4 v0x564c4c18d350_0;
    %pad/u 32;
    %pushi/vec4 0, 0, 32;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.0, 8;
    %vpi_call 2 68 "$display", "  \342\234\223 SUCCESS: SystemVerilog field extraction is correct!" {0 0 0};
    %vpi_call 2 69 "$display", "  \342\234\223 The fix is working properly" {0 0 0};
    %jmp T_0.1;
T_0.0 ;
    %vpi_call 2 71 "$display", "  \342\234\227 FAILURE: SystemVerilog field extraction is incorrect" {0 0 0};
    %vpi_call 2 72 "$display", "  Expected: td=0, rs1=7, rs2=0" {0 0 0};
    %vpi_call 2 73 "$display", "  Actual:   td=%0d, rs1=%0d, rs2=%0d", v0x564c4c18d510_0, v0x564c4c18d220_0, v0x564c4c18d350_0 {0 0 0};
T_0.1 ;
    %vpi_call 2 75 "$display", "\000" {0 0 0};
    %vpi_call 2 78 "$display", "=== Instruction Identification ===" {0 0 0};
    %load/vec4 v0x564c4c18cfa0_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0x564c4c14f360_0, 0, 7;
    %load/vec4 v0x564c4c18cfa0_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0x564c4c18d5f0_0, 0, 3;
    %load/vec4 v0x564c4c18cfa0_0;
    %parti/s 6, 25, 6;
    %store/vec4 v0x564c4c18d140_0, 0, 6;
    %load/vec4 v0x564c4c18cfa0_0;
    %parti/s 2, 10, 5;
    %store/vec4 v0x564c4c18d080_0, 0, 2;
    %load/vec4 v0x564c4c18cfa0_0;
    %parti/s 3, 44, 7;
    %store/vec4 v0x564c4c18d430_0, 0, 3;
    %load/vec4 v0x564c4c14f360_0;
    %cmpi/e 123, 0, 7;
    %flag_mov 8, 4;
    %jmp/0 T_0.2, 8;
    %pushi/vec4 0, 0, 32; draw_string_vec4
    %pushi/vec4 1414089797, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.3, 8;
T_0.2 ; End of true expr.
    %pushi/vec4 1313821791, 0, 32; draw_string_vec4
    %pushi/vec4 1414089797, 0, 32; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.3, 8;
 ; End of false expr.
    %blend;
T_0.3;
    %vpi_call 2 86 "$display", "  ace_op: 0x%02x (%s)", v0x564c4c14f360_0, S<0,vec4,u64> {1 0 0};
    %load/vec4 v0x564c4c18d5f0_0;
    %cmpi/e 6, 0, 3;
    %flag_mov 8, 4;
    %jmp/0 T_0.4, 8;
    %pushi/vec4 12593, 0, 32; draw_string_vec4
    %pushi/vec4 48, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.5, 8;
T_0.4 ; End of true expr.
    %pushi/vec4 1330923589, 0, 32; draw_string_vec4
    %pushi/vec4 82, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.5, 8;
 ; End of false expr.
    %blend;
T_0.5;
    %vpi_call 2 87 "$display", "  tuop: %0d (%s)", v0x564c4c18d5f0_0, S<0,vec4,u40> {1 0 0};
    %load/vec4 v0x564c4c18d140_0;
    %cmpi/e 1, 0, 6;
    %flag_mov 8, 4;
    %jmp/0 T_0.6, 8;
    %pushi/vec4 808464432, 0, 32; draw_string_vec4
    %pushi/vec4 12337, 0, 16; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.7, 8;
T_0.6 ; End of true expr.
    %pushi/vec4 5198920, 0, 32; draw_string_vec4
    %pushi/vec4 17746, 0, 16; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.7, 8;
 ; End of false expr.
    %blend;
T_0.7;
    %vpi_call 2 88 "$display", "  memuop: %0d (%s)", v0x564c4c18d140_0, S<0,vec4,u48> {1 0 0};
    %load/vec4 v0x564c4c18d080_0;
    %cmpi/e 1, 0, 2;
    %flag_mov 8, 4;
    %jmp/0 T_0.8, 8;
    %pushi/vec4 48, 0, 32; draw_string_vec4
    %pushi/vec4 49, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.9, 8;
T_0.8 ; End of true expr.
    %pushi/vec4 1330923589, 0, 32; draw_string_vec4
    %pushi/vec4 82, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.9, 8;
 ; End of false expr.
    %blend;
T_0.9;
    %vpi_call 2 89 "$display", "  lsuop: %0d (%s)", v0x564c4c18d080_0, S<0,vec4,u40> {1 0 0};
    %load/vec4 v0x564c4c18d430_0;
    %cmpi/e 1, 0, 3;
    %flag_mov 8, 4;
    %jmp/0 T_0.10, 8;
    %pushi/vec4 12336, 0, 32; draw_string_vec4
    %pushi/vec4 49, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/1 T_0.11, 8;
T_0.10 ; End of true expr.
    %pushi/vec4 1330923589, 0, 32; draw_string_vec4
    %pushi/vec4 82, 0, 8; draw_string_vec4
    %concat/vec4; draw_string_vec4
    %jmp/0 T_0.11, 8;
 ; End of false expr.
    %blend;
T_0.11;
    %vpi_call 2 90 "$display", "  second_tuop: %0d (%s)", v0x564c4c18d430_0, S<0,vec4,u40> {1 0 0};
    %load/vec4 v0x564c4c14f360_0;
    %cmpi/e 123, 0, 7;
    %jmp/0xz  T_0.12, 4;
    %load/vec4 v0x564c4c18d5f0_0;
    %cmpi/e 6, 0, 3;
    %jmp/0xz  T_0.14, 4;
    %load/vec4 v0x564c4c18d140_0;
    %cmpi/e 1, 0, 6;
    %jmp/0xz  T_0.16, 4;
    %load/vec4 v0x564c4c18d080_0;
    %cmpi/e 1, 0, 2;
    %jmp/0xz  T_0.18, 4;
    %load/vec4 v0x564c4c18d430_0;
    %cmpi/e 1, 0, 3;
    %jmp/0xz  T_0.20, 4;
    %vpi_call 2 98 "$display", "  \342\234\223 Instruction correctly identified as tld.trr.blk.mx48.share" {0 0 0};
    %jmp T_0.21;
T_0.20 ;
    %vpi_call 2 100 "$display", "  \342\234\227 second_tuop check failed" {0 0 0};
T_0.21 ;
    %jmp T_0.19;
T_0.18 ;
    %vpi_call 2 103 "$display", "  \342\234\227 lsuop check failed" {0 0 0};
T_0.19 ;
    %jmp T_0.17;
T_0.16 ;
    %vpi_call 2 106 "$display", "  \342\234\227 memuop check failed" {0 0 0};
T_0.17 ;
    %jmp T_0.15;
T_0.14 ;
    %vpi_call 2 109 "$display", "  \342\234\227 tuop check failed" {0 0 0};
T_0.15 ;
    %jmp T_0.13;
T_0.12 ;
    %vpi_call 2 112 "$display", "  \342\234\227 ace_op check failed" {0 0 0};
T_0.13 ;
    %vpi_call 2 114 "$display", "\000" {0 0 0};
    %vpi_call 2 116 "$display", "=== Test Summary ===" {0 0 0};
    %load/vec4 v0x564c4c18d510_0;
    %pad/u 32;
    %pushi/vec4 0, 0, 32;
    %cmp/e;
    %flag_get/vec4 4;
    %load/vec4 v0x564c4c18d220_0;
    %pad/u 32;
    %pushi/vec4 7, 0, 32;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %load/vec4 v0x564c4c18d350_0;
    %pad/u 32;
    %pushi/vec4 0, 0, 32;
    %cmp/e;
    %flag_get/vec4 4;
    %and;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.22, 8;
    %vpi_call 2 118 "$display", "\342\234\223 OVERALL SUCCESS: The SystemVerilog fix is working correctly!" {0 0 0};
    %vpi_call 2 119 "$display", "  Instruction 0x8003907b8200647b correctly decodes to:" {0 0 0};
    %vpi_call 2 120 "$display", "  tld.trr.blk.mx48.share t0, (x7), x0" {0 0 0};
    %jmp T_0.23;
T_0.22 ;
    %vpi_call 2 122 "$display", "\342\234\227 OVERALL FAILURE: The SystemVerilog fix needs more work" {0 0 0};
T_0.23 ;
    %vpi_call 2 125 "$finish" {0 0 0};
    %end;
    .thread T_0;
# The file index is used to find the file name in the following table.
:file_names 3;
    "N/A";
    "<interactive>";
    "test_systemverilog_simple.sv";
