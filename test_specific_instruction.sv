// Test for specific instruction 0x8003907b8200647b
// Should be decoded as tld.trr.blk.mx48.share

`timescale 1ns/1ps

import tile_decoder_pkg::*;

module test_specific_instruction;

    initial begin
        logic [31:0] word1, word2;
        logic [63:0] full_instruction;
        instr_collector_t collector;
        string result;
        instr_length_e expected_length;
        logic is_tile;
        
        $display("=== Testing Specific Instruction 0x8003907b8200647b ===");
        $display("");
        
        // 测试指令 0x8003907b8200647b
        word1 = 32'h8200647b;  // 低32位
        word2 = 32'h8003907b;  // 高32位
        full_instruction = {word2, word1};
        
        $display("Full instruction: 0x%016x", full_instruction);
        $display("Word 1 (low):     0x%08x", word1);
        $display("Word 2 (high):    0x%08x", word2);
        $display("");
        
        // 分析第一个字的字段
        $display("Word 1 analysis:");
        $display("  ACE_OP: 0x%02x", word1[6:0]);
        $display("  lsuop:  %0d", word1[11:10]);
        $display("  tuop:   %0d", word1[14:12]);
        $display("  memuop: %0d", word1[30:25]);
        $display("");
        
        // 分析第二个字的字段
        $display("Word 2 analysis:");
        $display("  ACE_OP: 0x%02x", word2[6:0]);
        $display("  tuop:   %0d", word2[14:12]);
        $display("  rs1:    %0d", word2[19:15]);
        $display("  Td:     %0d", word2[30:23]);
        $display("");
        
        // 测试函数调用
        $display("Function tests:");
        
        // 1. 检查是否为tile指令
        is_tile = is_tile_instruction(word1);
        $display("  is_tile_instruction(word1): %s", is_tile ? "TRUE" : "FALSE");
        
        // 2. 获取指令长度
        expected_length = get_instruction_length(word1);
        $display("  get_instruction_length(word1): %s", expected_length.name());
        
        // 3. 初始化收集器
        if (is_tile) begin
            collector = init_collector(word1);
            $display("  init_collector results:");
            $display("    collected_words: %0d", collector.collected_words);
            $display("    expected_length: %s", collector.expected_length.name());
            $display("    is_complete: %s", collector.is_complete ? "TRUE" : "FALSE");
            $display("    is_tile_instr: %s", collector.is_tile_instr ? "TRUE" : "FALSE");
            
            // 4. 添加第二个字（如果需要）
            if (!collector.is_complete) begin
                $display("  Adding second word...");
                collector = add_word_to_collector(collector, word2);
                $display("    collected_words: %0d", collector.collected_words);
                $display("    is_complete: %s", collector.is_complete ? "TRUE" : "FALSE");
                
                // 5. 反编译
                if (collector.is_complete) begin
                    result = disassemble_instruction(collector.instruction_data, collector.expected_length);
                    $display("  disassemble_instruction result: %s", result);
                    $display("");
                    
                    // 验证结果
                    $display("Expected: tld.trr.blk.mx48.share t0, (x7), x0");
                    $display("Actual:   %s", result);
                    
                    if (result == "tld.trr.blk.mx48.share t0, (x7), x0") begin
                        $display("Result: PASS ✓");
                    end else begin
                        $display("Result: FAIL ✗");
                    end
                end else begin
                    $display("ERROR: Instruction not complete after adding second word");
                end
            end else begin
                $display("ERROR: 64-bit instruction should not be complete after first word");
            end
        end else begin
            $display("ERROR: Word should be recognized as tile instruction");
        end
        
        $display("");
        $display("=== Test Complete ===");
        $finish;
    end

endmodule
