#! /usr/bin/vvp
:ivl_version "11.0 (stable)";
:ivl_delay_selection "TYPICAL";
:vpi_time_precision + 0;
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/system.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_sys.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/vhdl_textio.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2005_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/va_math.vpi";
:vpi_module "/usr/lib/x86_64-linux-gnu/ivl/v2009.vpi";
S_0x56003f264830 .scope package, "$unit" "$unit" 2 1;
 .timescale 0 0;
S_0x56003f23c190 .scope module, "fixed_test" "fixed_test" 3 2;
 .timescale 0 0;
S_0x56003f23c320 .scope begin, "$unm_blk_8" "$unm_blk_8" 3 44, 3 44 0, S_0x56003f23c190;
 .timescale 0 0;
v0x56003f23d750_0 .var "instruction_data", 127 0;
v0x56003f23dbb0_0 .var/str "result";
v0x56003f23dfa0_0 .var "test_instruction", 31 0;
S_0x56003f290280 .scope autofunction.str, "extract_sync_instruction_name" "extract_sync_instruction_name" 3 5, 3 5 0, S_0x56003f23c190;
 .timescale 0 0;
v0x56003f290480_0 .var "ace_op", 6 0;
v0x56003f290560_0 .var "ctrluop", 2 0;
; Variable extract_sync_instruction_name is string return value of scope S_0x56003f290280
v0x56003f290710_0 .var "instruction_data", 127 0;
v0x56003f2907f0_0 .var "isMem", 0 0;
v0x56003f290900_0 .var "tuop", 2 0;
v0x56003f2909e0_0 .var "waitop", 2 0;
TD_fixed_test.extract_sync_instruction_name ;
    %load/vec4 v0x56003f290710_0;
    %parti/s 7, 0, 2;
    %store/vec4 v0x56003f290480_0, 0, 7;
    %load/vec4 v0x56003f290480_0;
    %cmpi/ne 123, 0, 7;
    %jmp/0xz  T_0.0, 4;
    %pushi/str "unknown_non_tile";
    %ret/str 0; Assign to extract_sync_instruction_name
    %disable S_0x56003f290280;
T_0.0 ;
    %load/vec4 v0x56003f290710_0;
    %parti/s 3, 12, 5;
    %store/vec4 v0x56003f290900_0, 0, 3;
    %load/vec4 v0x56003f290900_0;
    %cmpi/e 5, 0, 3;
    %jmp/0xz  T_0.2, 4;
    %load/vec4 v0x56003f290710_0;
    %parti/s 3, 23, 6;
    %store/vec4 v0x56003f290560_0, 0, 3;
    %load/vec4 v0x56003f290710_0;
    %parti/s 3, 28, 6;
    %store/vec4 v0x56003f2909e0_0, 0, 3;
    %load/vec4 v0x56003f290560_0;
    %cmpi/e 1, 0, 3;
    %jmp/0xz  T_0.4, 4;
    %load/vec4 v0x56003f2909e0_0;
    %cmpi/e 7, 0, 3;
    %jmp/0xz  T_0.6, 4;
    %load/vec4 v0x56003f290710_0;
    %parti/s 1, 26, 6;
    %store/vec4 v0x56003f2907f0_0, 0, 1;
    %load/vec4 v0x56003f2907f0_0;
    %flag_set/vec4 8;
    %jmp/0xz  T_0.8, 8;
    %pushi/str "twait.mem";
    %ret/str 0; Assign to extract_sync_instruction_name
    %disable S_0x56003f290280;
    %jmp T_0.9;
T_0.8 ;
    %pushi/str "twait";
    %ret/str 0; Assign to extract_sync_instruction_name
    %disable S_0x56003f290280;
T_0.9 ;
    %jmp T_0.7;
T_0.6 ;
    %pushi/str "unknown_waitop";
    %ret/str 0; Assign to extract_sync_instruction_name
    %disable S_0x56003f290280;
T_0.7 ;
    %jmp T_0.5;
T_0.4 ;
    %pushi/str "unknown_ctrluop";
    %ret/str 0; Assign to extract_sync_instruction_name
    %disable S_0x56003f290280;
T_0.5 ;
    %jmp T_0.3;
T_0.2 ;
    %pushi/str "unknown_tuop";
    %ret/str 0; Assign to extract_sync_instruction_name
    %disable S_0x56003f290280;
T_0.3 ;
    %end;
    .scope S_0x56003f23c190;
T_1 ;
    %fork t_1, S_0x56003f23c320;
    %jmp t_0;
    .scope S_0x56003f23c320;
t_1 ;
    %vpi_call/w 3 49 "$display", "=== Fixed twait test ===" {0 0 0};
    %pushi/vec4 1887457403, 0, 32;
    %store/vec4 v0x56003f23dfa0_0, 0, 32;
    %pushi/vec4 0, 0, 96;
    %load/vec4 v0x56003f23dfa0_0;
    %concat/vec4; draw_concat_vec4
    %store/vec4 v0x56003f23d750_0, 0, 128;
    %vpi_call/w 3 55 "$display", "Testing instruction: 0x%08x", v0x56003f23dfa0_0 {0 0 0};
    %alloc S_0x56003f290280;
    %load/vec4 v0x56003f23d750_0;
    %store/vec4 v0x56003f290710_0, 0, 128;
    %callf/str TD_fixed_test.extract_sync_instruction_name, S_0x56003f290280;
    %free S_0x56003f290280;
    %store/str v0x56003f23dbb0_0;
    %vpi_call/w 3 58 "$display", "Result: %s", v0x56003f23dbb0_0 {0 0 0};
    %load/str v0x56003f23dbb0_0;
    %pushi/str "twait";
    %cmp/str;
    %flag_get/vec4 4;
    %flag_set/vec4 8;
    %jmp/0xz  T_1.0, 8;
    %vpi_call/w 3 61 "$display", "\342\234\223 SUCCESS: Instruction 0x7080507B correctly identified as 'twait'" {0 0 0};
    %jmp T_1.1;
T_1.0 ;
    %vpi_call/w 3 63 "$display", "\342\234\227 FAILURE: Expected 'twait', got '%s'", v0x56003f23dbb0_0 {0 0 0};
T_1.1 ;
    %vpi_call/w 3 66 "$display", "=== Test Complete ===" {0 0 0};
    %end;
    .scope S_0x56003f23c190;
t_0 %join;
    %end;
    .thread T_1;
# The file index is used to find the file name in the following table.
:file_names 4;
    "N/A";
    "<interactive>";
    "-";
    "fixed_test.sv";
